# Simple test script for JML v1.12
Write-Host "=== JML v1.12 Simple Test ===" -ForegroundColor Cyan

# Test 1: Check if files exist
Write-Host "`n--- File Existence Test ---" -ForegroundColor Yellow
$files = @(
    "JML_v1.12.ps1",
    "AdminAccountConfig.psd1",
    "Modules\JML-Configuration.psm1",
    "Modules\JML-Security.psm1",
    "Modules\JML-Logging.psm1",
    "Modules\JML-Utilities.psm1"
)

foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "✅ $file exists" -ForegroundColor Green
    } else {
        Write-Host "❌ $file missing" -ForegroundColor Red
    }
}

# Test 2: Check configuration
Write-Host "`n--- Configuration Test ---" -ForegroundColor Yellow
try {
    $config = Import-PowerShellDataFile "AdminAccountConfig.psd1"
    Write-Host "✅ Configuration loaded" -ForegroundColor Green
    Write-Host "   Domain: $($config.DefaultDomain)" -ForegroundColor White
} catch {
    Write-Host "❌ Configuration failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 3: Check credentials
Write-Host "`n--- Credential Test ---" -ForegroundColor Yellow
if (Test-Path "SecureCredentials.xml") {
    Write-Host "✅ Main credential file exists" -ForegroundColor Green
} else {
    Write-Host "❌ Main credential file missing" -ForegroundColor Red
}

if (Test-Path "Modules\SecureCredentials.xml") {
    Write-Host "✅ Module credential file exists" -ForegroundColor Green
} else {
    Write-Host "❌ Module credential file missing" -ForegroundColor Red
}

# Test 4: Try to load a module
Write-Host "`n--- Module Loading Test ---" -ForegroundColor Yellow
try {
    Import-Module ".\Modules\JML-Configuration.psm1" -Force
    Write-Host "✅ JML-Configuration module loaded" -ForegroundColor Green
    
    # Test a function
    if (Get-Command "Get-JMLConfiguration" -ErrorAction SilentlyContinue) {
        Write-Host "✅ Get-JMLConfiguration function available" -ForegroundColor Green
    } else {
        Write-Host "❌ Get-JMLConfiguration function not found" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Module loading failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n--- Simple Test Complete ---" -ForegroundColor Cyan
