# Test script for Option 1 - Create Admin Account
# This script tests the Create Admin Account functionality with comprehensive error handling

Write-Host "=== Testing Option 1: Create Admin Account ===" -ForegroundColor Cyan

# Set up test environment
$ErrorActionPreference = "Continue"
$testResults = @()

# Test data
$testUser = "testadmin001"
$testTicket = "TESTIT-49364"

Write-Host "`n--- Loading JML Script and Modules ---" -ForegroundColor Yellow

# First, let's check if the main script can be loaded
try {
    # Check if the script file exists
    if (-not (Test-Path ".\JML_v1.12.ps1")) {
        throw "JML_v1.12.ps1 not found in current directory"
    }
    
    Write-Host "✅ JML_v1.12.ps1 file found" -ForegroundColor Green
    
    # Try to load the script content to check for syntax errors
    $scriptContent = Get-Content ".\JML_v1.12.ps1" -Raw
    Write-Host "✅ Script content loaded successfully" -ForegroundColor Green
    
    # Check for key functions in the script
    if ($scriptContent -match "function New-AdminAccount") {
        Write-Host "✅ New-AdminAccount function found in script" -ForegroundColor Green
    } else {
        Write-Host "⚠️ New-AdminAccount function not found in script" -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "❌ Failed to load script: $($_.Exception.Message)" -ForegroundColor Red
    $testResults += "Script Loading: FAILED - $($_.Exception.Message)"
}

Write-Host "`n--- Testing Module Availability ---" -ForegroundColor Yellow

# Check if required modules are available
$requiredModules = @(
    "JML-Configuration",
    "JML-Security", 
    "JML-Logging",
    "JML-Utilities",
    "JML-ActiveDirectory",
    "JML-Jira",
    "JML-Email"
)

foreach ($module in $requiredModules) {
    $modulePath = ".\Modules\$module.psm1"
    if (Test-Path $modulePath) {
        Write-Host "✅ $module module found" -ForegroundColor Green
        $testResults += "$module Module: FOUND"
    } else {
        Write-Host "❌ $module module missing" -ForegroundColor Red
        $testResults += "$module Module: MISSING"
    }
}

Write-Host "`n--- Testing Configuration Loading ---" -ForegroundColor Yellow

try {
    if (Test-Path ".\AdminAccountConfig.psd1") {
        $config = Import-PowerShellDataFile ".\AdminAccountConfig.psd1"
        Write-Host "✅ Configuration file loaded successfully" -ForegroundColor Green
        Write-Host "   Domain: $($config.DefaultDomain)" -ForegroundColor White
        Write-Host "   Jira Server: $($config.JiraServer)" -ForegroundColor White
        $testResults += "Configuration Loading: PASSED"
    } else {
        Write-Host "❌ Configuration file not found" -ForegroundColor Red
        $testResults += "Configuration Loading: FAILED - File not found"
    }
} catch {
    Write-Host "❌ Failed to load configuration: $($_.Exception.Message)" -ForegroundColor Red
    $testResults += "Configuration Loading: FAILED - $($_.Exception.Message)"
}

Write-Host "`n--- Testing Credential Storage ---" -ForegroundColor Yellow

try {
    # Check for credential files
    $credFiles = @(".\SecureCredentials.xml", ".\Modules\SecureCredentials.xml")
    $credFound = $false
    
    foreach ($credFile in $credFiles) {
        if (Test-Path $credFile) {
            Write-Host "✅ Credential file found: $credFile" -ForegroundColor Green
            $credFound = $true
            
            # Try to load credentials
            try {
                $creds = Import-Clixml $credFile
                Write-Host "   Contains $($creds.Keys.Count) credential entries" -ForegroundColor White
                $testResults += "Credential File ($credFile): LOADED"
            } catch {
                Write-Host "   ⚠️ Could not load credential file: $($_.Exception.Message)" -ForegroundColor Yellow
                $testResults += "Credential File ($credFile): FOUND but UNREADABLE"
            }
        }
    }
    
    if (-not $credFound) {
        Write-Host "❌ No credential files found" -ForegroundColor Red
        $testResults += "Credential Storage: NO FILES FOUND"
    }
} catch {
    Write-Host "❌ Credential storage test failed: $($_.Exception.Message)" -ForegroundColor Red
    $testResults += "Credential Storage: FAILED - $($_.Exception.Message)"
}

Write-Host "`n--- Test Results Summary ---" -ForegroundColor Cyan
foreach ($result in $testResults) {
    Write-Host "  $result" -ForegroundColor White
}

Write-Host "`n--- Option 1 Pre-Test Complete ---" -ForegroundColor Cyan
Write-Host "Next: Attempt to load and execute the main script..." -ForegroundColor Yellow
