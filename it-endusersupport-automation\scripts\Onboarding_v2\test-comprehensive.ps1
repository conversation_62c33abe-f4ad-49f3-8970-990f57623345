# Comprehensive Test Script for JML v1.12
# This script performs systematic testing of all JML functionality

Write-Host "=== JML v1.12 Comprehensive Testing Suite ===" -ForegroundColor Cyan
Write-Host "Test Date: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Gray
Write-Host ""

# Initialize test results
$testResults = @{
    StartTime = Get-Date
    Tests = @()
    Passed = 0
    Failed = 0
    Warnings = 0
}

function Add-TestResult {
    param(
        [string]$TestName,
        [string]$Status,  # PASSED, FAILED, WARNING
        [string]$Details,
        [string]$ErrorMessage = ""
    )
    
    $result = @{
        TestName = $TestName
        Status = $Status
        Details = $Details
        ErrorMessage = $ErrorMessage
        Timestamp = Get-Date
    }
    
    $testResults.Tests += $result
    
    switch ($Status) {
        "PASSED" { 
            $testResults.Passed++
            Write-Host "✅ $TestName - PASSED" -ForegroundColor Green
            if ($Details) { Write-Host "   $Details" -ForegroundColor Gray }
        }
        "FAILED" { 
            $testResults.Failed++
            Write-Host "❌ $TestName - FAILED" -ForegroundColor Red
            if ($Details) { Write-Host "   $Details" -ForegroundColor Gray }
            if ($ErrorMessage) { Write-Host "   Error: $ErrorMessage" -ForegroundColor Red }
        }
        "WARNING" { 
            $testResults.Warnings++
            Write-Host "⚠️ $TestName - WARNING" -ForegroundColor Yellow
            if ($Details) { Write-Host "   $Details" -ForegroundColor Gray }
        }
    }
}

# Test 1: File Existence
Write-Host "`n--- Test 1: File Existence ---" -ForegroundColor Yellow

$requiredFiles = @(
    "JML_v1.12.ps1",
    "AdminAccountConfig.psd1",
    "Modules\JML-Configuration.psm1",
    "Modules\JML-Security.psm1",
    "Modules\JML-Logging.psm1",
    "Modules\JML-Utilities.psm1",
    "Modules\JML-Email.psm1",
    "Modules\JML-Jira.psm1"
)

foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Add-TestResult -TestName "File Existence: $file" -Status "PASSED" -Details "File found"
    } else {
        Add-TestResult -TestName "File Existence: $file" -Status "FAILED" -Details "File missing"
    }
}

# Test 2: Configuration Loading
Write-Host "`n--- Test 2: Configuration Loading ---" -ForegroundColor Yellow

try {
    $config = Import-PowerShellDataFile "AdminAccountConfig.psd1"
    Add-TestResult -TestName "Configuration Loading" -Status "PASSED" -Details "Domain: $($config.DefaultDomain), Jira: $($config.JiraServer)"
    
    # Test specific configuration values
    if ($config.DefaultDomain) {
        Add-TestResult -TestName "Configuration: DefaultDomain" -Status "PASSED" -Details $config.DefaultDomain
    } else {
        Add-TestResult -TestName "Configuration: DefaultDomain" -Status "FAILED" -Details "DefaultDomain not found"
    }
    
    if ($config.JiraServer) {
        Add-TestResult -TestName "Configuration: JiraServer" -Status "PASSED" -Details $config.JiraServer
    } else {
        Add-TestResult -TestName "Configuration: JiraServer" -Status "FAILED" -Details "JiraServer not found"
    }
    
} catch {
    Add-TestResult -TestName "Configuration Loading" -Status "FAILED" -Details "Failed to load configuration" -ErrorMessage $_.Exception.Message
}

# Test 3: Credential Files
Write-Host "`n--- Test 3: Credential Storage ---" -ForegroundColor Yellow

$credFiles = @("SecureCredentials.xml", "Modules\SecureCredentials.xml")
$credentialsFound = $false

foreach ($credFile in $credFiles) {
    if (Test-Path $credFile) {
        try {
            $creds = Import-Clixml $credFile
            Add-TestResult -TestName "Credential File: $credFile" -Status "PASSED" -Details "Contains $($creds.Keys.Count) entries"
            $credentialsFound = $true
        } catch {
            Add-TestResult -TestName "Credential File: $credFile" -Status "WARNING" -Details "File exists but cannot be read" -ErrorMessage $_.Exception.Message
        }
    } else {
        Add-TestResult -TestName "Credential File: $credFile" -Status "WARNING" -Details "File not found"
    }
}

if (-not $credentialsFound) {
    Add-TestResult -TestName "Credential Storage Overall" -Status "FAILED" -Details "No readable credential files found"
}

# Test 4: Module Syntax Check
Write-Host "`n--- Test 4: Module Syntax Validation ---" -ForegroundColor Yellow

$moduleFiles = Get-ChildItem "Modules\*.psm1" -ErrorAction SilentlyContinue

foreach ($moduleFile in $moduleFiles) {
    try {
        # Try to parse the module file for syntax errors
        $null = [System.Management.Automation.PSParser]::Tokenize((Get-Content $moduleFile.FullName -Raw), [ref]$null)
        Add-TestResult -TestName "Module Syntax: $($moduleFile.Name)" -Status "PASSED" -Details "No syntax errors"
    } catch {
        Add-TestResult -TestName "Module Syntax: $($moduleFile.Name)" -Status "FAILED" -Details "Syntax error detected" -ErrorMessage $_.Exception.Message
    }
}

# Test 5: ShowVersion Parameter Test
Write-Host "`n--- Test 5: ShowVersion Parameter Test ---" -ForegroundColor Yellow

try {
    # Test the ShowVersion parameter
    $versionOutput = & powershell -ExecutionPolicy Bypass -File "JML_v1.12.ps1" -ShowVersion 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        Add-TestResult -TestName "ShowVersion Parameter" -Status "PASSED" -Details "Version information displayed successfully"
    } else {
        Add-TestResult -TestName "ShowVersion Parameter" -Status "FAILED" -Details "ShowVersion parameter failed" -ErrorMessage "Exit code: $LASTEXITCODE"
    }
} catch {
    Add-TestResult -TestName "ShowVersion Parameter" -Status "FAILED" -Details "ShowVersion test failed" -ErrorMessage $_.Exception.Message
}

Write-Host "`n--- Test Results Summary ---" -ForegroundColor Cyan
Write-Host "Total Tests: $($testResults.Tests.Count)" -ForegroundColor White
Write-Host "Passed: $($testResults.Passed)" -ForegroundColor Green
Write-Host "Failed: $($testResults.Failed)" -ForegroundColor Red
Write-Host "Warnings: $($testResults.Warnings)" -ForegroundColor Yellow

$duration = (Get-Date) - $testResults.StartTime
Write-Host "Test Duration: $($duration.TotalSeconds.ToString('F2')) seconds" -ForegroundColor Gray

# Calculate success rate
$successRate = if ($testResults.Tests.Count -gt 0) {
    ($testResults.Passed / $testResults.Tests.Count) * 100
} else { 0 }

Write-Host "Success Rate: $($successRate.ToString('F1'))%" -ForegroundColor $(if ($successRate -ge 80) { "Green" } elseif ($successRate -ge 60) { "Yellow" } else { "Red" })

Write-Host "`n--- Detailed Test Results ---" -ForegroundColor Cyan
foreach ($test in $testResults.Tests) {
    $statusColor = switch ($test.Status) {
        "PASSED" { "Green" }
        "FAILED" { "Red" }
        "WARNING" { "Yellow" }
    }
    Write-Host "$($test.TestName): $($test.Status)" -ForegroundColor $statusColor
    if ($test.Details) { Write-Host "  $($test.Details)" -ForegroundColor Gray }
    if ($test.ErrorMessage) { Write-Host "  Error: $($test.ErrorMessage)" -ForegroundColor Red }
}

Write-Host "`n--- Comprehensive Test Complete ---" -ForegroundColor Cyan
