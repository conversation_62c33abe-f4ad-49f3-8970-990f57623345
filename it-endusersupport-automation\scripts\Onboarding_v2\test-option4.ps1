# Simple test for Option 4 - System Information
Write-Host "=== Testing Option 4 - System Information ===" -ForegroundColor Cyan

# Create input file
"4" | Out-File -FilePath "input.txt" -Encoding ASCII

try {
    # Run the script with input redirection
    $output = cmd /c "powershell -ExecutionPolicy Bypass -Command `"& '.\JML_v1.12.ps1' < input.txt`""
    
    Write-Host "Script output:" -ForegroundColor Yellow
    Write-Host $output
    
    if ($output -match "System Information") {
        Write-Host "✅ System Information option works correctly" -ForegroundColor Green
    } else {
        Write-Host "❌ System Information option may have issues" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Test failed: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    # Cleanup
    Remove-Item "input.txt" -ErrorAction SilentlyContinue
}

Write-Host "=== Test Complete ===" -ForegroundColor Cyan
