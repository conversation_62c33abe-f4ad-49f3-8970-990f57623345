# Test script to isolate configuration loading issue
Write-Host "=== JML Configuration Test ===" -ForegroundColor Cyan
Write-Host ""

# Test 1: Basic PowerShell functionality
Write-Host "Test 1: Basic PowerShell functionality" -ForegroundColor Yellow
try {
    $testVar = "Hello World"
    Write-Host "  ✅ Basic variable assignment works: $testVar" -ForegroundColor Green
} catch {
    Write-Host "  ❌ Basic PowerShell test failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: Configuration file existence
Write-Host ""
Write-Host "Test 2: Configuration file existence" -ForegroundColor Yellow
$configPath = ".\AdminAccountConfig.psd1"
if (Test-Path $configPath) {
    Write-Host "  ✅ Configuration file exists: $configPath" -ForegroundColor Green
} else {
    Write-Host "  ❌ Configuration file not found: $configPath" -ForegroundColor Red
}

# Test 3: Configuration file loading
Write-Host ""
Write-Host "Test 3: Configuration file loading" -ForegroundColor Yellow
try {
    $config = Import-PowerShellDataFile -Path $configPath -ErrorAction Stop
    Write-Host "  ✅ Configuration file loaded successfully" -ForegroundColor Green
    Write-Host "  ✅ Default domain: $($config.ScriptSettings.DefaultDomain)" -ForegroundColor Green
} catch {
    Write-Host "  ❌ Configuration file loading failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "  ❌ Error type: $($_.Exception.GetType().Name)" -ForegroundColor Red
}

# Test 4: Module loading
Write-Host ""
Write-Host "Test 4: Module loading" -ForegroundColor Yellow
$modulePath = ".\Modules\JML-Configuration.psm1"
if (Test-Path $modulePath) {
    Write-Host "  ✅ Configuration module file exists" -ForegroundColor Green
    try {
        Import-Module $modulePath -Force -Global -ErrorAction Stop
        Write-Host "  ✅ Configuration module loaded successfully" -ForegroundColor Green
        
        # Test if function is available
        $function = Get-Command Initialize-SmartConfiguration -ErrorAction SilentlyContinue
        if ($function) {
            Write-Host "  ✅ Initialize-SmartConfiguration function available" -ForegroundColor Green
        } else {
            Write-Host "  ❌ Initialize-SmartConfiguration function not found" -ForegroundColor Red
        }
    } catch {
        Write-Host "  ❌ Configuration module loading failed: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "  ❌ Configuration module file not found: $modulePath" -ForegroundColor Red
}

# Test 5: Smart configuration initialization
Write-Host ""
Write-Host "Test 5: Smart configuration initialization" -ForegroundColor Yellow
try {
    $result = Initialize-SmartConfiguration -ConfigPath $configPath -ErrorAction Stop
    Write-Host "  ✅ Smart configuration initialized: $result" -ForegroundColor Green
    
    $moduleConfig = Get-ModuleConfiguration -ErrorAction SilentlyContinue
    if ($moduleConfig) {
        Write-Host "  ✅ Module configuration retrieved successfully" -ForegroundColor Green
        Write-Host "  ✅ Domain from module config: $($moduleConfig.ScriptSettings.DefaultDomain)" -ForegroundColor Green
    } else {
        Write-Host "  ❌ Module configuration not available" -ForegroundColor Red
    }
} catch {
    Write-Host "  ❌ Smart configuration initialization failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "  ❌ Error type: $($_.Exception.GetType().Name)" -ForegroundColor Red
    Write-Host "  ❌ Stack trace: $($_.ScriptStackTrace)" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== Test Complete ===" -ForegroundColor Cyan
