# Module Status Display Test for JML v1.12
# This script tests the module status display functionality and identifies issues

Write-Host "=== JML v1.12 Module Status Display Test ===" -ForegroundColor Cyan
Write-Host "Test Date: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Gray
Write-Host ""

# Test Results
$testResults = @()

function Add-TestResult {
    param($TestName, $Status, $Details, $ErrorMessage = "")
    
    $result = @{
        TestName = $TestName
        Status = $Status
        Details = $Details
        ErrorMessage = $ErrorMessage
        Timestamp = Get-Date
    }
    
    $script:testResults += $result
    
    $color = switch ($Status) {
        "PASSED" { "Green" }
        "FAILED" { "Red" }
        "WARNING" { "Yellow" }
    }
    
    Write-Host "$(if($Status -eq 'PASSED'){'✅'}elseif($Status -eq 'FAILED'){'❌'}else{'⚠️'}) $TestName - $Status" -ForegroundColor $color
    if ($Details) { Write-Host "   $Details" -ForegroundColor Gray }
    if ($ErrorMessage) { Write-Host "   Error: $ErrorMessage" -ForegroundColor Red }
}

# Test 1: Get-JMLVersion Function Analysis
Write-Host "`n--- Test 1: Get-JMLVersion Function Analysis ---" -ForegroundColor Yellow

try {
    $scriptContent = Get-Content "JML_v1.12.ps1" -Raw
    
    if ($scriptContent -match "function Get-JMLVersion") {
        Add-TestResult -TestName "Get-JMLVersion Function" -Status "PASSED" -Details "Function definition found"
        
        # Check module detection logic
        if ($scriptContent -match "Get-Module.*Name.*moduleName") {
            Add-TestResult -TestName "Module Detection Logic" -Status "PASSED" -Details "Get-Module logic found"
        } else {
            Add-TestResult -TestName "Module Detection Logic" -Status "WARNING" -Details "Module detection logic not found or different pattern"
        }
        
        # Check function availability detection
        if ($scriptContent -match "Get-Command.*ErrorAction.*SilentlyContinue") {
            Add-TestResult -TestName "Function Detection Logic" -Status "PASSED" -Details "Get-Command logic found"
        } else {
            Add-TestResult -TestName "Function Detection Logic" -Status "WARNING" -Details "Function detection logic not found"
        }
        
    } else {
        Add-TestResult -TestName "Get-JMLVersion Function" -Status "FAILED" -Details "Function definition not found"
    }
    
} catch {
    Add-TestResult -TestName "Get-JMLVersion Analysis" -Status "FAILED" -Details "Could not analyze function" -ErrorMessage $_.Exception.Message
}

# Test 2: Module Loading vs Detection Comparison
Write-Host "`n--- Test 2: Module Loading vs Detection Comparison ---" -ForegroundColor Yellow

try {
    # Load modules and check actual status
    $moduleTests = @(
        "JML-Configuration",
        "JML-Security",
        "JML-Logging", 
        "JML-Utilities",
        "JML-Email",
        "JML-Jira",
        "JML-ActiveDirectory",
        "JML-Setup"
    )
    
    foreach ($moduleName in $moduleTests) {
        $modulePath = ".\Modules\$moduleName.psm1"
        
        # Check if module file exists
        if (Test-Path $modulePath) {
            try {
                # Try to load module
                if (Get-Module -Name $moduleName -ErrorAction SilentlyContinue) {
                    Remove-Module -Name $moduleName -Force
                }
                
                Import-Module $modulePath -Force -ErrorAction Stop
                $loadedModule = Get-Module -Name $moduleName
                
                if ($loadedModule) {
                    Add-TestResult -TestName "Module Load Test: $moduleName" -Status "PASSED" -Details "Loaded with $($loadedModule.ExportedFunctions.Count) functions"
                    
                    # Test function availability
                    $functionCount = 0
                    foreach ($func in $loadedModule.ExportedFunctions.Keys) {
                        if (Get-Command $func -ErrorAction SilentlyContinue) {
                            $functionCount++
                        }
                    }
                    
                    Add-TestResult -TestName "Function Availability: $moduleName" -Status "PASSED" -Details "$functionCount of $($loadedModule.ExportedFunctions.Count) functions available"
                } else {
                    Add-TestResult -TestName "Module Load Test: $moduleName" -Status "WARNING" -Details "Module imported but not found in Get-Module"
                }
                
            } catch {
                Add-TestResult -TestName "Module Load Test: $moduleName" -Status "WARNING" -Details "Module failed to load" -ErrorMessage $_.Exception.Message
            }
        } else {
            Add-TestResult -TestName "Module Load Test: $moduleName" -Status "FAILED" -Details "Module file not found"
        }
    }
    
} catch {
    Add-TestResult -TestName "Module Loading Comparison" -Status "FAILED" -Details "Could not test module loading" -ErrorMessage $_.Exception.Message
}

# Test 3: Show-SystemInformation Function Analysis
Write-Host "`n--- Test 3: Show-SystemInformation Function Analysis ---" -ForegroundColor Yellow

try {
    $scriptContent = Get-Content "JML_v1.12.ps1" -Raw
    
    if ($scriptContent -match "function Show-SystemInformation") {
        Add-TestResult -TestName "Show-SystemInformation Function" -Status "PASSED" -Details "Function definition found"
        
        # Check if it calls Get-JMLVersion
        if ($scriptContent -match "Show-SystemInformation.*Get-JMLVersion") {
            Add-TestResult -TestName "Version Function Call" -Status "PASSED" -Details "Get-JMLVersion call found"
        } else {
            Add-TestResult -TestName "Version Function Call" -Status "WARNING" -Details "Get-JMLVersion call not found in Show-SystemInformation"
        }
        
        # Check module status display logic
        if ($scriptContent -match "Module Status.*foreach.*module") {
            Add-TestResult -TestName "Module Status Display" -Status "PASSED" -Details "Module status display logic found"
        } else {
            Add-TestResult -TestName "Module Status Display" -Status "WARNING" -Details "Module status display logic not found"
        }
        
    } else {
        Add-TestResult -TestName "Show-SystemInformation Function" -Status "FAILED" -Details "Function definition not found"
    }
    
} catch {
    Add-TestResult -TestName "Show-SystemInformation Analysis" -Status "FAILED" -Details "Could not analyze function" -ErrorMessage $_.Exception.Message
}

# Test 4: Module Detection Method Testing
Write-Host "`n--- Test 4: Module Detection Method Testing ---" -ForegroundColor Yellow

try {
    # Test different module detection methods
    $testModules = @("JML-Configuration", "JML-Security", "JML-Logging", "JML-Utilities")
    
    foreach ($moduleName in $testModules) {
        $modulePath = ".\Modules\$moduleName.psm1"
        
        if (Test-Path $modulePath) {
            # Load module first
            try {
                if (Get-Module -Name $moduleName -ErrorAction SilentlyContinue) {
                    Remove-Module -Name $moduleName -Force
                }
                Import-Module $modulePath -Force -ErrorAction Stop
                
                # Method 1: Get-Module by name
                $method1 = Get-Module -Name $moduleName -ErrorAction SilentlyContinue
                $method1Result = $null -ne $method1
                
                # Method 2: Check key functions
                $keyFunctions = switch ($moduleName) {
                    "JML-Configuration" { "Initialize-SmartConfiguration" }
                    "JML-Security" { "Get-SecureCredential" }
                    "JML-Logging" { "Write-SecureLog" }
                    "JML-Utilities" { "Get-ValidatedUPN" }
                }
                
                $method2Result = $null -ne (Get-Command $keyFunctions -ErrorAction SilentlyContinue)
                
                Add-TestResult -TestName "Detection Methods: $moduleName" -Status "PASSED" -Details "Get-Module: $method1Result, Function Check: $method2Result"
                
                if ($method1Result -ne $method2Result) {
                    Add-TestResult -TestName "Detection Mismatch: $moduleName" -Status "WARNING" -Details "Detection methods disagree - this may cause display issues"
                }
                
            } catch {
                Add-TestResult -TestName "Detection Methods: $moduleName" -Status "WARNING" -Details "Could not test detection methods" -ErrorMessage $_.Exception.Message
            }
        }
    }
    
} catch {
    Add-TestResult -TestName "Module Detection Method Testing" -Status "FAILED" -Details "Could not test detection methods" -ErrorMessage $_.Exception.Message
}

# Test 5: Version Display Issue Diagnosis
Write-Host "`n--- Test 5: Version Display Issue Diagnosis ---" -ForegroundColor Yellow

try {
    # Check for version-related issues in Get-JMLVersion
    $scriptContent = Get-Content "JML_v1.12.ps1" -Raw
    
    # Look for version assignment logic
    if ($scriptContent -match "Version.*module\.Version.*ToString") {
        Add-TestResult -TestName "Version Assignment Logic" -Status "PASSED" -Details "Version assignment logic found"
    } else {
        Add-TestResult -TestName "Version Assignment Logic" -Status "WARNING" -Details "Version assignment logic not found or different pattern"
    }
    
    # Check for fallback version logic
    if ($scriptContent -match "1\.12.*else.*Unknown") {
        Add-TestResult -TestName "Version Fallback Logic" -Status "PASSED" -Details "Version fallback logic found"
    } else {
        Add-TestResult -TestName "Version Fallback Logic" -Status "WARNING" -Details "Version fallback logic not found"
    }
    
    # Check for module.Version check
    if ($scriptContent -match "module\.Version.*0\.0") {
        Add-TestResult -TestName "Version Zero Check" -Status "PASSED" -Details "Version 0.0 check found"
    } else {
        Add-TestResult -TestName "Version Zero Check" -Status "WARNING" -Details "Version 0.0 check not found"
    }
    
} catch {
    Add-TestResult -TestName "Version Display Issue Diagnosis" -Status "FAILED" -Details "Could not diagnose version issues" -ErrorMessage $_.Exception.Message
}

# Test Summary
Write-Host "`n--- Module Status Display Test Summary ---" -ForegroundColor Cyan

$passed = ($testResults | Where-Object { $_.Status -eq "PASSED" }).Count
$failed = ($testResults | Where-Object { $_.Status -eq "FAILED" }).Count
$warnings = ($testResults | Where-Object { $_.Status -eq "WARNING" }).Count

Write-Host "Total Tests: $($testResults.Count)" -ForegroundColor White
Write-Host "Passed: $passed" -ForegroundColor Green
Write-Host "Failed: $failed" -ForegroundColor Red
Write-Host "Warnings: $warnings" -ForegroundColor Yellow

$successRate = if ($testResults.Count -gt 0) {
    ($passed / $testResults.Count) * 100
} else { 0 }

Write-Host "Success Rate: $($successRate.ToString('F1'))%" -ForegroundColor $(if ($successRate -ge 80) { "Green" } elseif ($successRate -ge 60) { "Yellow" } else { "Red" })

# Issue Analysis
Write-Host "`n--- Module Status Display Issue Analysis ---" -ForegroundColor Cyan

$detectionMismatches = $testResults | Where-Object { $_.TestName -like "Detection Mismatch:*" }
if ($detectionMismatches.Count -gt 0) {
    Write-Host "⚠️ DETECTION MISMATCHES FOUND" -ForegroundColor Yellow
    Write-Host "The following modules have detection method mismatches:" -ForegroundColor Yellow
    foreach ($mismatch in $detectionMismatches) {
        Write-Host "  - $($mismatch.TestName)" -ForegroundColor Yellow
    }
    Write-Host "This explains the module status display inconsistency issue." -ForegroundColor Yellow
} else {
    Write-Host "✅ NO DETECTION MISMATCHES FOUND" -ForegroundColor Green
    Write-Host "Module detection methods are consistent." -ForegroundColor Green
}

Write-Host "`n--- Module Status Display Test Complete ---" -ForegroundColor Cyan
