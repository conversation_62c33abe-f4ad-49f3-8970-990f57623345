# Email Integration Test for JML v1.12
# This script tests the email integration functionality

Write-Host "=== JML v1.12 Email Integration Test ===" -ForegroundColor Cyan
Write-Host "Test Date: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Gray
Write-Host ""

# Test Results
$testResults = @()

function Add-TestResult {
    param($TestName, $Status, $Details, $ErrorMessage = "")
    
    $result = @{
        TestName = $TestName
        Status = $Status
        Details = $Details
        ErrorMessage = $ErrorMessage
        Timestamp = Get-Date
    }
    
    $script:testResults += $result
    
    $color = switch ($Status) {
        "PASSED" { "Green" }
        "FAILED" { "Red" }
        "WARNING" { "Yellow" }
    }
    
    Write-Host "$(if($Status -eq 'PASSED'){'✅'}elseif($Status -eq 'FAILED'){'❌'}else{'⚠️'}) $TestName - $Status" -ForegroundColor $color
    if ($Details) { Write-Host "   $Details" -ForegroundColor Gray }
    if ($ErrorMessage) { Write-Host "   Error: $ErrorMessage" -ForegroundColor Red }
}

# Test 1: Email Module File Check
Write-Host "`n--- Test 1: Email Module File Check ---" -ForegroundColor Yellow

if (Test-Path "Modules\JML-Email.psm1") {
    Add-TestResult -TestName "Email Module File" -Status "PASSED" -Details "JML-Email.psm1 found"
    
    try {
        $emailContent = Get-Content "Modules\JML-Email.psm1" -Raw
        $functionCount = ([regex]::Matches($emailContent, "function\s+[\w-]+")).Count
        Add-TestResult -TestName "Email Module Functions" -Status "PASSED" -Details "Found $functionCount function definitions"
    } catch {
        Add-TestResult -TestName "Email Module Content" -Status "FAILED" -Details "Could not read module content" -ErrorMessage $_.Exception.Message
    }
} else {
    Add-TestResult -TestName "Email Module File" -Status "FAILED" -Details "JML-Email.psm1 not found"
}

# Test 2: Required Email Functions Check
Write-Host "`n--- Test 2: Required Email Functions Check ---" -ForegroundColor Yellow

$requiredEmailFunctions = @(
    "Send-EmailWithRetry",
    "Send-EmailNotification",
    "Send-DeletionEmailNotification",
    "Send-ResetEmailNotification"
)

if (Test-Path "Modules\JML-Email.psm1") {
    try {
        $emailContent = Get-Content "Modules\JML-Email.psm1" -Raw
        
        foreach ($func in $requiredEmailFunctions) {
            if ($emailContent -match "function\s+$func") {
                Add-TestResult -TestName "Email Function: $func" -Status "PASSED" -Details "Function definition found"
            } else {
                Add-TestResult -TestName "Email Function: $func" -Status "FAILED" -Details "Function definition not found"
            }
        }
        
        # Check export statement
        if ($emailContent -match "Export-ModuleMember.*Send-EmailWithRetry.*Send-EmailNotification.*Send-DeletionEmailNotification.*Send-ResetEmailNotification") {
            Add-TestResult -TestName "Email Function Export" -Status "PASSED" -Details "All functions properly exported"
        } else {
            Add-TestResult -TestName "Email Function Export" -Status "WARNING" -Details "Export statement may be incomplete"
        }
        
    } catch {
        Add-TestResult -TestName "Email Function Analysis" -Status "FAILED" -Details "Could not analyze functions" -ErrorMessage $_.Exception.Message
    }
}

# Test 3: Email Configuration Check
Write-Host "`n--- Test 3: Email Configuration Check ---" -ForegroundColor Yellow

try {
    $config = Import-PowerShellDataFile "AdminAccountConfig.psd1"
    
    if ($config.Email) {
        Add-TestResult -TestName "Email Configuration Section" -Status "PASSED" -Details "Email configuration found"
        
        # Check specific email settings
        $emailSettings = @(
            @{Key = "SmtpServer"; Description = "SMTP server address"},
            @{Key = "SmtpPort"; Description = "SMTP port number"},
            @{Key = "DefaultFrom"; Description = "Default sender address"},
            @{Key = "SupportEmail"; Description = "Support team email"},
            @{Key = "EnableNotifications"; Description = "Email notifications enabled"}
        )
        
        foreach ($setting in $emailSettings) {
            if ($config.Email.ContainsKey($setting.Key)) {
                Add-TestResult -TestName "Email Setting: $($setting.Key)" -Status "PASSED" -Details "$($setting.Description): $($config.Email[$setting.Key])"
            } else {
                Add-TestResult -TestName "Email Setting: $($setting.Key)" -Status "WARNING" -Details "$($setting.Description) not configured"
            }
        }
        
        # Check retry settings
        if ($config.Email.RetrySettings) {
            Add-TestResult -TestName "Email Retry Settings" -Status "PASSED" -Details "MaxAttempts: $($config.Email.RetrySettings.MaxAttempts), BaseDelay: $($config.Email.RetrySettings.BaseDelay)"
        } else {
            Add-TestResult -TestName "Email Retry Settings" -Status "WARNING" -Details "Retry settings not configured"
        }
        
    } else {
        Add-TestResult -TestName "Email Configuration Section" -Status "FAILED" -Details "Email configuration not found"
    }
    
} catch {
    Add-TestResult -TestName "Email Configuration Analysis" -Status "FAILED" -Details "Could not load configuration" -ErrorMessage $_.Exception.Message
}

# Test 4: Email Module Loading Test
Write-Host "`n--- Test 4: Email Module Loading Test ---" -ForegroundColor Yellow

try {
    # Remove module if already loaded
    if (Get-Module -Name "JML-Email" -ErrorAction SilentlyContinue) {
        Remove-Module -Name "JML-Email" -Force
    }
    
    # Try to load the email module
    Import-Module ".\Modules\JML-Email.psm1" -Force -ErrorAction Stop
    Add-TestResult -TestName "Email Module Loading" -Status "PASSED" -Details "Module loaded successfully"
    
    # Check if functions are available
    $loadedFunctions = 0
    foreach ($func in $requiredEmailFunctions) {
        if (Get-Command $func -ErrorAction SilentlyContinue) {
            $loadedFunctions++
        }
    }
    
    Add-TestResult -TestName "Email Functions Available" -Status "PASSED" -Details "$loadedFunctions of $($requiredEmailFunctions.Count) functions available"
    
} catch {
    Add-TestResult -TestName "Email Module Loading" -Status "FAILED" -Details "Could not load email module" -ErrorMessage $_.Exception.Message
}

# Test 5: Email Function Parameter Analysis
Write-Host "`n--- Test 5: Email Function Parameter Analysis ---" -ForegroundColor Yellow

if (Test-Path "Modules\JML-Email.psm1") {
    try {
        $emailContent = Get-Content "Modules\JML-Email.psm1" -Raw
        
        # Check for required parameters in email functions
        $parameterChecks = @(
            @{Function = "Send-EmailNotification"; Parameter = "LogPath"; Description = "Log file path parameter"},
            @{Function = "Send-EmailNotification"; Parameter = "AdminUserUPN"; Description = "Admin user UPN parameter"},
            @{Function = "Send-DeletionEmailNotification"; Parameter = "LogPath"; Description = "Log file path parameter"},
            @{Function = "Send-ResetEmailNotification"; Parameter = "NewPassword"; Description = "New password parameter"},
            @{Function = "Send-EmailWithRetry"; Parameter = "From"; Description = "From address parameter"},
            @{Function = "Send-EmailWithRetry"; Parameter = "To"; Description = "To address parameter"}
        )
        
        foreach ($check in $parameterChecks) {
            $pattern = "function\s+$($check.Function).*param.*$($check.Parameter)"
            if ($emailContent -match $pattern) {
                Add-TestResult -TestName "Parameter: $($check.Function).$($check.Parameter)" -Status "PASSED" -Details $check.Description
            } else {
                Add-TestResult -TestName "Parameter: $($check.Function).$($check.Parameter)" -Status "WARNING" -Details "$($check.Description) not found or different structure"
            }
        }
        
    } catch {
        Add-TestResult -TestName "Email Parameter Analysis" -Status "FAILED" -Details "Could not analyze parameters" -ErrorMessage $_.Exception.Message
    }
}

# Test 6: Email Security Features
Write-Host "`n--- Test 6: Email Security Features ---" -ForegroundColor Yellow

if (Test-Path "Modules\JML-Email.psm1") {
    try {
        $emailContent = Get-Content "Modules\JML-Email.psm1" -Raw
        
        # Check for security features
        $securityChecks = @(
            @{Name = "Data Redaction"; Pattern = "Protect-SensitiveData"},
            @{Name = "Secure Logging"; Pattern = "Write-SecureLog"},
            @{Name = "Input Validation"; Pattern = "ValidateNotNullOrEmpty"},
            @{Name = "Error Handling"; Pattern = "try.*catch"},
            @{Name = "SSL/TLS Support"; Pattern = "UseSSL|EnableSsl"}
        )
        
        foreach ($check in $securityChecks) {
            $matches = [regex]::Matches($emailContent, $check.Pattern, [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
            if ($matches.Count -gt 0) {
                Add-TestResult -TestName "Email Security: $($check.Name)" -Status "PASSED" -Details "$($matches.Count) instances found"
            } else {
                Add-TestResult -TestName "Email Security: $($check.Name)" -Status "WARNING" -Details "Security feature not found"
            }
        }
        
    } catch {
        Add-TestResult -TestName "Email Security Analysis" -Status "FAILED" -Details "Could not analyze security features" -ErrorMessage $_.Exception.Message
    }
}

# Test Summary
Write-Host "`n--- Email Integration Test Summary ---" -ForegroundColor Cyan

$passed = ($testResults | Where-Object { $_.Status -eq "PASSED" }).Count
$failed = ($testResults | Where-Object { $_.Status -eq "FAILED" }).Count
$warnings = ($testResults | Where-Object { $_.Status -eq "WARNING" }).Count

Write-Host "Total Tests: $($testResults.Count)" -ForegroundColor White
Write-Host "Passed: $passed" -ForegroundColor Green
Write-Host "Failed: $failed" -ForegroundColor Red
Write-Host "Warnings: $warnings" -ForegroundColor Yellow

$successRate = if ($testResults.Count -gt 0) {
    ($passed / $testResults.Count) * 100
} else { 0 }

Write-Host "Success Rate: $($successRate.ToString('F1'))%" -ForegroundColor $(if ($successRate -ge 80) { "Green" } elseif ($successRate -ge 60) { "Yellow" } else { "Red" })

# Email Integration Readiness Assessment
Write-Host "`n--- Email Integration Readiness Assessment ---" -ForegroundColor Cyan

if ($passed -ge ($testResults.Count * 0.8)) {
    Write-Host "🎉 EMAIL INTEGRATION READY FOR TESTING" -ForegroundColor Green
    Write-Host "The email integration appears to be properly configured and ready for testing." -ForegroundColor Green
} elseif ($passed -ge ($testResults.Count * 0.6)) {
    Write-Host "⚠️ EMAIL INTEGRATION PARTIALLY READY" -ForegroundColor Yellow
    Write-Host "Some issues detected but core functionality appears available." -ForegroundColor Yellow
} else {
    Write-Host "❌ EMAIL INTEGRATION NEEDS ATTENTION" -ForegroundColor Red
    Write-Host "Multiple issues detected that should be resolved before testing." -ForegroundColor Red
}

Write-Host "`n--- Email Integration Test Complete ---" -ForegroundColor Cyan
