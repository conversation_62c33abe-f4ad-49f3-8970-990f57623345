# Debug script to check module loading
Write-Host "=== Module Loading Debug ===" -ForegroundColor Cyan

# Import modules manually
$modules = @(
    "JML-Configuration.psm1",
    "JML-Security.psm1", 
    "JML-Logging.psm1",
    "JML-Utilities.psm1",
    "JML-Email.psm1",
    "JML-Jira.psm1"
)

foreach ($module in $modules) {
    $modulePath = ".\Modules\$module"
    Write-Host "Importing: $module" -ForegroundColor Yellow
    try {
        Import-Module $modulePath -Force -Global -ErrorAction Stop
        Write-Host "  ✅ Success" -ForegroundColor Green
    } catch {
        Write-Host "  ❌ Failed: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "Loaded JML modules:" -ForegroundColor Cyan
$loadedModules = Get-Module | Where-Object { $_.Name -like "*JML*" }
foreach ($mod in $loadedModules) {
    Write-Host "  - $($mod.Name) (Version: $($mod.Version), Functions: $($mod.ExportedFunctions.Count))" -ForegroundColor Green
}

Write-Host ""
Write-Host "Testing Get-JMLVersion function..." -ForegroundColor Cyan

# Test the module detection logic
$jmlModules = @(
    "JML-Configuration", "JML-Security", "JML-Logging", "JML-Utilities",
    "JML-ActiveDirectory", "JML-Email", "JML-Jira", "JML-Setup"
)

foreach ($moduleName in $jmlModules) {
    $module = Get-Module -Name $moduleName -ErrorAction SilentlyContinue
    if ($module) {
        Write-Host "  ✅ Found: $moduleName (Version: $($module.Version), Functions: $($module.ExportedFunctions.Count))" -ForegroundColor Green
    } else {
        Write-Host "  ❌ Missing: $moduleName" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "=== Debug Complete ===" -ForegroundColor Cyan
