# Comprehensive test for Jira integration
Write-Host "=== JML Jira Integration Test ===" -ForegroundColor Cyan
Write-Host ""

# Import required modules
try {
    Import-Module ".\Modules\JML-Security.psm1" -Force -Global -ErrorAction Stop
    Import-Module ".\Modules\JML-Jira.psm1" -Force -Global -ErrorAction Stop
    Write-Host "✅ Required modules loaded successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to load required modules: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Load configuration
try {
    $config = Import-PowerShellDataFile -Path ".\AdminAccountConfig.psd1" -ErrorAction Stop
    Write-Host "✅ Configuration loaded successfully" -ForegroundColor Green
    Write-Host "  Jira Server: $($config.Jira.ServerUrl)" -ForegroundColor Gray
} catch {
    Write-Host "❌ Failed to load configuration: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Test 1: Credential Retrieval for Jira
Write-Host ""
Write-Host "Test 1: Jira Credential Retrieval" -ForegroundColor Yellow
try {
    $jiraUsername = Get-SecureCredential -CredentialName "AdminScript-JiraUsername" -Purpose "Jira integration test"
    $jiraApiToken = Get-SecureCredential -CredentialName "AdminScript-JiraApiToken" -Purpose "Jira integration test"
    
    if ($jiraUsername -and $jiraApiToken) {
        Write-Host "  ✅ Jira credentials retrieved successfully" -ForegroundColor Green
        Write-Host "    Username: $jiraUsername" -ForegroundColor Gray
        Write-Host "    API Token: [SECURE STRING]" -ForegroundColor Gray
        
        # Create PSCredential object
        $jiraCredential = New-Object System.Management.Automation.PSCredential($jiraUsername, $jiraApiToken)
        Write-Host "  ✅ PSCredential object created successfully" -ForegroundColor Green
    } else {
        Write-Host "  ❌ Failed to retrieve Jira credentials" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "  ❌ Credential retrieval failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Test 2: Jira Connection Initialization
Write-Host ""
Write-Host "Test 2: Jira Connection Initialization" -ForegroundColor Yellow
try {
    Write-Host "  Initializing connection to: $($config.Jira.ServerUrl)" -ForegroundColor Gray
    $connectionResult = Initialize-JiraConnection -ServerUrl $config.Jira.ServerUrl -Credential $jiraCredential -TestConnection
    
    if ($connectionResult) {
        Write-Host "  ✅ Jira connection initialized successfully" -ForegroundColor Green
    } else {
        Write-Host "  ❌ Jira connection initialization failed" -ForegroundColor Red
    }
} catch {
    Write-Host "  ❌ Jira connection error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "    This may be due to network connectivity or authentication issues" -ForegroundColor Yellow
}

# Test 3: Test Ticket Validation (TESTIT-49364)
Write-Host ""
Write-Host "Test 3: Test Ticket Validation" -ForegroundColor Yellow
$testTicket = "TESTIT-49364"
try {
    Write-Host "  Testing ticket: $testTicket" -ForegroundColor Gray
    $ticketValidation = Test-JiraTicketValidation -TicketKey $testTicket -ExpectedWorkType $config.Jira.ExpectedWorkTypes.CreateAdmin
    
    if ($ticketValidation.IsValid) {
        Write-Host "  ✅ Ticket validation successful" -ForegroundColor Green
        Write-Host "    Ticket Key: $($ticketValidation.TicketKey)" -ForegroundColor Gray
        Write-Host "    Work Type: $($ticketValidation.WorkType)" -ForegroundColor Gray
        Write-Host "    Status: $($ticketValidation.Status)" -ForegroundColor Gray
        
        if ($ticketValidation.Fields) {
            Write-Host "    Custom Fields:" -ForegroundColor Gray
            foreach ($field in $ticketValidation.Fields.GetEnumerator()) {
                Write-Host "      $($field.Key): $($field.Value)" -ForegroundColor Gray
            }
        }
    } else {
        Write-Host "  ❌ Ticket validation failed" -ForegroundColor Red
        Write-Host "    Error: $($ticketValidation.ErrorMessage)" -ForegroundColor Red
    }
} catch {
    Write-Host "  ❌ Ticket validation error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "    This may be due to network connectivity or ticket access issues" -ForegroundColor Yellow
}

# Test 4: Comment Posting Test
Write-Host ""
Write-Host "Test 4: Comment Posting Test" -ForegroundColor Yellow
try {
    $testComment = "JML System Test - Credential storage and Jira integration test completed successfully at $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
    Write-Host "  Posting test comment to ticket: $testTicket" -ForegroundColor Gray
    
    $commentResult = Add-EnhancedJiraComment -TicketKey $testTicket -CommentText $testComment -UseADF -IncludeTimestamp
    
    if ($commentResult) {
        Write-Host "  ✅ Test comment posted successfully" -ForegroundColor Green
    } else {
        Write-Host "  ❌ Failed to post test comment" -ForegroundColor Red
    }
} catch {
    Write-Host "  ❌ Comment posting error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "    This may be due to network connectivity or permission issues" -ForegroundColor Yellow
}

# Test 5: Available Jira Functions
Write-Host ""
Write-Host "Test 5: Available Jira Functions" -ForegroundColor Yellow
$jiraFunctions = Get-Command -Module "JML-Jira" -ErrorAction SilentlyContinue
if ($jiraFunctions) {
    Write-Host "  ✅ Jira module functions available: $($jiraFunctions.Count)" -ForegroundColor Green
    foreach ($func in $jiraFunctions) {
        Write-Host "    - $($func.Name)" -ForegroundColor Gray
    }
} else {
    Write-Host "  ❌ No Jira functions found" -ForegroundColor Red
}

# Test 6: Error Handling Test
Write-Host ""
Write-Host "Test 6: Error Handling Test" -ForegroundColor Yellow
try {
    Write-Host "  Testing with invalid ticket key..." -ForegroundColor Gray
    $invalidTicketTest = Test-JiraTicketValidation -TicketKey "INVALID-12345" -ExpectedWorkType "Test"
    
    if (-not $invalidTicketTest.IsValid) {
        Write-Host "  ✅ Error handling working correctly for invalid tickets" -ForegroundColor Green
        Write-Host "    Error message: $($invalidTicketTest.ErrorMessage)" -ForegroundColor Gray
    } else {
        Write-Host "  ⚠️ Unexpected success with invalid ticket" -ForegroundColor Yellow
    }
} catch {
    Write-Host "  ✅ Exception handling working correctly: $($_.Exception.Message)" -ForegroundColor Green
}

Write-Host ""
Write-Host "=== Jira Integration Test Complete ===" -ForegroundColor Cyan
