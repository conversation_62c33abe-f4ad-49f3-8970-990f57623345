# Admin Functions Test for JML v1.12
# This script tests the core admin account functions

Write-Host "=== JML v1.12 Admin Functions Test ===" -ForegroundColor Cyan
Write-Host "Test Date: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Gray
Write-Host ""

# Test Results
$testResults = @()

function Add-TestResult {
    param($TestName, $Status, $Details, $ErrorMessage = "")
    
    $result = @{
        TestName = $TestName
        Status = $Status
        Details = $Details
        ErrorMessage = $ErrorMessage
        Timestamp = Get-Date
    }
    
    $script:testResults += $result
    
    $color = switch ($Status) {
        "PASSED" { "Green" }
        "FAILED" { "Red" }
        "WARNING" { "Yellow" }
    }
    
    Write-Host "$(if($Status -eq 'PASSED'){'✅'}elseif($Status -eq 'FAILED'){'❌'}else{'⚠️'}) $TestName - $Status" -ForegroundColor $color
    if ($Details) { Write-Host "   $Details" -ForegroundColor Gray }
    if ($ErrorMessage) { Write-Host "   Error: $ErrorMessage" -ForegroundColor Red }
}

# Test 1: Admin Function Existence
Write-Host "`n--- Test 1: Admin Function Existence ---" -ForegroundColor Yellow

$adminFunctions = @(
    "New-AdminAccount",
    "Remove-AdminAccount", 
    "Reset-StdAdminAccount"
)

try {
    $scriptContent = Get-Content "JML_v1.12.ps1" -Raw
    
    foreach ($func in $adminFunctions) {
        if ($scriptContent -match "function $func") {
            Add-TestResult -TestName "Function: $func" -Status "PASSED" -Details "Function definition found"
        } else {
            Add-TestResult -TestName "Function: $func" -Status "FAILED" -Details "Function definition not found"
        }
    }
} catch {
    Add-TestResult -TestName "Script Analysis" -Status "FAILED" -Details "Could not analyze script" -ErrorMessage $_.Exception.Message
}

# Test 2: Function Parameter Analysis
Write-Host "`n--- Test 2: Function Parameter Analysis ---" -ForegroundColor Yellow

try {
    $scriptContent = Get-Content "JML_v1.12.ps1" -Raw
    
    # Test New-AdminAccount parameters
    if ($scriptContent -match "function New-AdminAccount\s*\{[^}]*param\s*\([^)]*TicketKey[^)]*\)") {
        Add-TestResult -TestName "New-AdminAccount Parameters" -Status "PASSED" -Details "TicketKey parameter found"
    } else {
        Add-TestResult -TestName "New-AdminAccount Parameters" -Status "WARNING" -Details "TicketKey parameter not found or different structure"
    }
    
    # Test parameter validation patterns
    if ($scriptContent -match "ValidatePattern.*\^\[A-Z\]\+.*\d\+") {
        Add-TestResult -TestName "Ticket Key Validation" -Status "PASSED" -Details "Ticket key validation pattern found"
    } else {
        Add-TestResult -TestName "Ticket Key Validation" -Status "WARNING" -Details "Ticket key validation pattern not found"
    }
    
} catch {
    Add-TestResult -TestName "Parameter Analysis" -Status "FAILED" -Details "Could not analyze parameters" -ErrorMessage $_.Exception.Message
}

# Test 3: Jira Integration in Functions
Write-Host "`n--- Test 3: Jira Integration in Functions ---" -ForegroundColor Yellow

try {
    $scriptContent = Get-Content "JML_v1.12.ps1" -Raw
    
    # Check for Jira integration calls
    $jiraIntegrationChecks = @(
        @{Name = "Initialize-JiraConnection"; Pattern = "Initialize-JiraConnection"},
        @{Name = "Test-JiraTicketValidation"; Pattern = "Test-JiraTicketValidation"},
        @{Name = "Add-EnhancedJiraComment"; Pattern = "Add-EnhancedJiraComment"},
        @{Name = "Jira Credential Retrieval"; Pattern = "Get-SecureCredential.*Jira"}
    )
    
    foreach ($check in $jiraIntegrationChecks) {
        if ($scriptContent -match $check.Pattern) {
            Add-TestResult -TestName "Jira Integration: $($check.Name)" -Status "PASSED" -Details "Integration call found"
        } else {
            Add-TestResult -TestName "Jira Integration: $($check.Name)" -Status "WARNING" -Details "Integration call not found"
        }
    }
    
} catch {
    Add-TestResult -TestName "Jira Integration Analysis" -Status "FAILED" -Details "Could not analyze Jira integration" -ErrorMessage $_.Exception.Message
}

# Test 4: Active Directory Integration
Write-Host "`n--- Test 4: Active Directory Integration ---" -ForegroundColor Yellow

try {
    $scriptContent = Get-Content "JML_v1.12.ps1" -Raw
    
    # Check for AD function calls
    $adIntegrationChecks = @(
        @{Name = "User Creation"; Pattern = "New-ADUser|Create.*User"},
        @{Name = "User Deletion"; Pattern = "Remove-ADUser|Delete.*User"},
        @{Name = "Password Reset"; Pattern = "Set-ADAccountPassword|Reset.*Password"},
        @{Name = "UPN Validation"; Pattern = "Get-ValidatedUPN"}
    )
    
    foreach ($check in $adIntegrationChecks) {
        if ($scriptContent -match $check.Pattern) {
            Add-TestResult -TestName "AD Integration: $($check.Name)" -Status "PASSED" -Details "AD operation found"
        } else {
            Add-TestResult -TestName "AD Integration: $($check.Name)" -Status "WARNING" -Details "AD operation not found (may use module functions)"
        }
    }
    
} catch {
    Add-TestResult -TestName "AD Integration Analysis" -Status "FAILED" -Details "Could not analyze AD integration" -ErrorMessage $_.Exception.Message
}

# Test 5: Email Notification Integration
Write-Host "`n--- Test 5: Email Notification Integration ---" -ForegroundColor Yellow

try {
    $scriptContent = Get-Content "JML_v1.12.ps1" -Raw
    
    # Check for email function calls
    $emailIntegrationChecks = @(
        @{Name = "Email Notification"; Pattern = "Send-EmailNotification"},
        @{Name = "Deletion Email"; Pattern = "Send-DeletionEmailNotification"},
        @{Name = "Reset Email"; Pattern = "Send-ResetEmailNotification"},
        @{Name = "Email with Retry"; Pattern = "Send-EmailWithRetry"}
    )
    
    foreach ($check in $emailIntegrationChecks) {
        if ($scriptContent -match $check.Pattern) {
            Add-TestResult -TestName "Email Integration: $($check.Name)" -Status "PASSED" -Details "Email function call found"
        } else {
            Add-TestResult -TestName "Email Integration: $($check.Name)" -Status "WARNING" -Details "Email function call not found"
        }
    }
    
} catch {
    Add-TestResult -TestName "Email Integration Analysis" -Status "FAILED" -Details "Could not analyze email integration" -ErrorMessage $_.Exception.Message
}

# Test 6: Error Handling and Logging
Write-Host "`n--- Test 6: Error Handling and Logging ---" -ForegroundColor Yellow

try {
    $scriptContent = Get-Content "JML_v1.12.ps1" -Raw
    
    # Check for error handling patterns
    $errorHandlingChecks = @(
        @{Name = "Try-Catch Blocks"; Pattern = "try\s*\{.*catch"},
        @{Name = "Secure Logging"; Pattern = "Write-SecureLog"},
        @{Name = "Error Messages"; Pattern = "Write-Error|throw"},
        @{Name = "Audit Trail"; Pattern = "AuditTrail"}
    )
    
    foreach ($check in $errorHandlingChecks) {
        $matches = [regex]::Matches($scriptContent, $check.Pattern, [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
        if ($matches.Count -gt 0) {
            Add-TestResult -TestName "Error Handling: $($check.Name)" -Status "PASSED" -Details "$($matches.Count) instances found"
        } else {
            Add-TestResult -TestName "Error Handling: $($check.Name)" -Status "WARNING" -Details "Pattern not found"
        }
    }
    
} catch {
    Add-TestResult -TestName "Error Handling Analysis" -Status "FAILED" -Details "Could not analyze error handling" -ErrorMessage $_.Exception.Message
}

# Test 7: Security Features
Write-Host "`n--- Test 7: Security Features ---" -ForegroundColor Yellow

try {
    $scriptContent = Get-Content "JML_v1.12.ps1" -Raw
    
    # Check for security features
    $securityChecks = @(
        @{Name = "Data Redaction"; Pattern = "Protect-SensitiveData"},
        @{Name = "Credential Security"; Pattern = "Get-SecureCredential"},
        @{Name = "Input Validation"; Pattern = "ValidatePattern|Confirm-InputSecurity"},
        @{Name = "Secure Password"; Pattern = "New-SecurePassword"}
    )
    
    foreach ($check in $securityChecks) {
        if ($scriptContent -match $check.Pattern) {
            Add-TestResult -TestName "Security: $($check.Name)" -Status "PASSED" -Details "Security feature found"
        } else {
            Add-TestResult -TestName "Security: $($check.Name)" -Status "WARNING" -Details "Security feature not found"
        }
    }
    
} catch {
    Add-TestResult -TestName "Security Analysis" -Status "FAILED" -Details "Could not analyze security features" -ErrorMessage $_.Exception.Message
}

# Test Summary
Write-Host "`n--- Admin Functions Test Summary ---" -ForegroundColor Cyan

$passed = ($testResults | Where-Object { $_.Status -eq "PASSED" }).Count
$failed = ($testResults | Where-Object { $_.Status -eq "FAILED" }).Count
$warnings = ($testResults | Where-Object { $_.Status -eq "WARNING" }).Count

Write-Host "Total Tests: $($testResults.Count)" -ForegroundColor White
Write-Host "Passed: $passed" -ForegroundColor Green
Write-Host "Failed: $failed" -ForegroundColor Red
Write-Host "Warnings: $warnings" -ForegroundColor Yellow

$successRate = if ($testResults.Count -gt 0) {
    ($passed / $testResults.Count) * 100
} else { 0 }

Write-Host "Success Rate: $($successRate.ToString('F1'))%" -ForegroundColor $(if ($successRate -ge 80) { "Green" } elseif ($successRate -ge 60) { "Yellow" } else { "Red" })

# Readiness Assessment
Write-Host "`n--- Admin Functions Readiness Assessment ---" -ForegroundColor Cyan

if ($passed -ge ($testResults.Count * 0.8)) {
    Write-Host "🎉 ADMIN FUNCTIONS READY FOR TESTING" -ForegroundColor Green
    Write-Host "The admin account functions appear to be properly implemented and ready for live testing." -ForegroundColor Green
} elseif ($passed -ge ($testResults.Count * 0.6)) {
    Write-Host "⚠️ ADMIN FUNCTIONS PARTIALLY READY" -ForegroundColor Yellow
    Write-Host "Some issues detected but core functionality appears available." -ForegroundColor Yellow
} else {
    Write-Host "❌ ADMIN FUNCTIONS NEED ATTENTION" -ForegroundColor Red
    Write-Host "Multiple issues detected that should be resolved before testing." -ForegroundColor Red
}

Write-Host "`n--- Admin Functions Test Complete ---" -ForegroundColor Cyan
