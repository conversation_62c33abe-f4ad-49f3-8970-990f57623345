# Test script to systematically test JML menu options
Write-Host "=== JML Menu Options Test ===" -ForegroundColor Cyan
Write-Host ""

# Function to test a menu option
function Test-MenuOption {
    param(
        [string]$OptionNumber,
        [string]$OptionName,
        [string]$ExpectedBehavior,
        [string[]]$InputSequence = @()
    )
    
    Write-Host "Testing Option $OptionNumber - $OptionName" -ForegroundColor Yellow
    Write-Host "Expected: $ExpectedBehavior" -ForegroundColor Gray
    
    try {
        # Create input sequence for the test
        $allInputs = @($OptionNumber) + $InputSequence + @("6")  # Always end with exit
        $inputString = $allInputs -join "`n"
        
        # Run the script with input
        $process = Start-Process -FilePath "powershell.exe" -ArgumentList @(
            "-ExecutionPolicy", "Bypass",
            "-Command", "Set-Location 'C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\Onboarding_v2'; echo '$inputString' | .\JML_v1.12.ps1"
        ) -PassThru -WindowStyle Hidden -RedirectStandardOutput "temp_output.txt" -RedirectStandardError "temp_error.txt"
        
        # Wait for completion with timeout
        $completed = $process.WaitForExit(30000)  # 30 second timeout
        
        if ($completed) {
            $output = Get-Content "temp_output.txt" -Raw -ErrorAction SilentlyContinue
            $error = Get-Content "temp_error.txt" -Raw -ErrorAction SilentlyContinue
            
            if ($output -and $output.Length -gt 0) {
                Write-Host "  ✅ Option $OptionNumber completed successfully" -ForegroundColor Green
                
                # Check for specific success indicators
                if ($OptionNumber -eq "4" -and $output -match "System Information") {
                    Write-Host "  ✅ System Information displayed correctly" -ForegroundColor Green
                } elseif ($OptionNumber -eq "5" -and $output -match "Setup") {
                    Write-Host "  ✅ Setup functionality accessed" -ForegroundColor Green
                } elseif ($OptionNumber -eq "6" -and $output -match "Thank you") {
                    Write-Host "  ✅ Exit message displayed correctly" -ForegroundColor Green
                }
                
                # Check for errors in output
                if ($output -match "ERROR|Failed|Exception") {
                    Write-Host "  ⚠️ Errors detected in output" -ForegroundColor Yellow
                }
            } else {
                Write-Host "  ❌ No output received" -ForegroundColor Red
            }
            
            if ($error -and $error.Length -gt 0) {
                Write-Host "  ⚠️ Errors: $error" -ForegroundColor Yellow
            }
        } else {
            Write-Host "  ❌ Test timed out after 30 seconds" -ForegroundColor Red
            $process.Kill()
        }
        
        # Cleanup temp files
        Remove-Item "temp_output.txt" -ErrorAction SilentlyContinue
        Remove-Item "temp_error.txt" -ErrorAction SilentlyContinue
        
    } catch {
        Write-Host "  ❌ Test failed: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    Write-Host ""
}

# Test each menu option
Write-Host "Starting systematic menu testing..." -ForegroundColor Cyan
Write-Host ""

# Test Option 4 - System Information (safest to test first)
Test-MenuOption -OptionNumber "4" -OptionName "System Information" -ExpectedBehavior "Display system and module information"

# Test Option 6 - Exit
Test-MenuOption -OptionNumber "6" -OptionName "Exit" -ExpectedBehavior "Clean exit with thank you message"

# Test Option 5 - Setup
Test-MenuOption -OptionNumber "5" -OptionName "Run Setup" -ExpectedBehavior "Display setup options and validation"

# Test invalid option
Test-MenuOption -OptionNumber "9" -OptionName "Invalid Option" -ExpectedBehavior "Error message for invalid choice"

Write-Host "=== Menu Testing Complete ===" -ForegroundColor Cyan
