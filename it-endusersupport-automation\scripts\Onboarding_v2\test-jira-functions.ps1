# Jira Integration Function Test for JML v1.12
# This script tests Jira integration by loading modules and testing functions

Write-Host "=== JML v1.12 Jira Integration Test ===" -ForegroundColor Cyan
Write-Host "Test Date: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Gray
Write-Host ""

# Test Results
$testResults = @()

function Add-TestResult {
    param($TestName, $Status, $Details, $ErrorMessage = "")
    
    $result = @{
        TestName = $TestName
        Status = $Status
        Details = $Details
        ErrorMessage = $ErrorMessage
        Timestamp = Get-Date
    }
    
    $script:testResults += $result
    
    $color = switch ($Status) {
        "PASSED" { "Green" }
        "FAILED" { "Red" }
        "WARNING" { "Yellow" }
    }
    
    Write-Host "$(if($Status -eq 'PASSED'){'✅'}elseif($Status -eq 'FAILED'){'❌'}else{'⚠️'}) $TestName - $Status" -ForegroundColor $color
    if ($Details) { Write-Host "   $Details" -ForegroundColor Gray }
    if ($ErrorMessage) { Write-Host "   Error: $ErrorMessage" -ForegroundColor Red }
}

# Test 1: Check Jira Module File
Write-Host "`n--- Test 1: Jira Module File Check ---" -ForegroundColor Yellow

if (Test-Path "Modules\JML-Jira.psm1") {
    Add-TestResult -TestName "Jira Module File" -Status "PASSED" -Details "JML-Jira.psm1 found"
    
    try {
        $jiraContent = Get-Content "Modules\JML-Jira.psm1" -Raw
        $functionCount = ([regex]::Matches($jiraContent, "function\s+[\w-]+")).Count
        Add-TestResult -TestName "Jira Module Functions" -Status "PASSED" -Details "Found $functionCount function definitions"
    } catch {
        Add-TestResult -TestName "Jira Module Content" -Status "FAILED" -Details "Could not read module content" -ErrorMessage $_.Exception.Message
    }
} else {
    Add-TestResult -TestName "Jira Module File" -Status "FAILED" -Details "JML-Jira.psm1 not found"
}

# Test 2: Check Required Jira Functions
Write-Host "`n--- Test 2: Required Jira Functions Check ---" -ForegroundColor Yellow

$requiredJiraFunctions = @(
    "Initialize-JiraConnection",
    "Test-JiraTicketValidation",
    "Add-EnhancedJiraComment",
    "Add-EnhancedJiraAttachment",
    "Format-JiraCommentADF",
    "Format-JiraCommentWiki",
    "Get-JiraCustomFieldValue",
    "Get-JiraErrorCategory",
    "Invoke-JiraOperationWithRetry",
    "Test-JiraAttachmentValidation",
    "Test-JiraErrorRetryable"
)

if (Test-Path "Modules\JML-Jira.psm1") {
    try {
        $jiraContent = Get-Content "Modules\JML-Jira.psm1" -Raw
        
        foreach ($func in $requiredJiraFunctions) {
            if ($jiraContent -match "function\s+$func") {
                Add-TestResult -TestName "Jira Function: $func" -Status "PASSED" -Details "Function definition found"
            } else {
                Add-TestResult -TestName "Jira Function: $func" -Status "FAILED" -Details "Function definition not found"
            }
        }
    } catch {
        Add-TestResult -TestName "Jira Function Analysis" -Status "FAILED" -Details "Could not analyze functions" -ErrorMessage $_.Exception.Message
    }
}

# Test 3: Check Configuration for Jira Settings
Write-Host "`n--- Test 3: Jira Configuration Check ---" -ForegroundColor Yellow

try {
    $config = Import-PowerShellDataFile "AdminAccountConfig.psd1"
    
    if ($config.JiraServer) {
        Add-TestResult -TestName "Jira Server Configuration" -Status "PASSED" -Details $config.JiraServer
    } else {
        Add-TestResult -TestName "Jira Server Configuration" -Status "FAILED" -Details "JiraServer not configured"
    }
    
    if ($config.TestTicket) {
        Add-TestResult -TestName "Test Ticket Configuration" -Status "PASSED" -Details $config.TestTicket
    } else {
        Add-TestResult -TestName "Test Ticket Configuration" -Status "WARNING" -Details "TestTicket not configured"
    }
    
    # Check Jira-specific settings
    if ($config.JiraIntegration) {
        Add-TestResult -TestName "Jira Integration Settings" -Status "PASSED" -Details "Jira integration settings found"
        
        if ($config.JiraIntegration.RetryAttempts) {
            Add-TestResult -TestName "Jira Retry Configuration" -Status "PASSED" -Details "RetryAttempts: $($config.JiraIntegration.RetryAttempts)"
        }
        
        if ($config.JiraIntegration.TimeoutSeconds) {
            Add-TestResult -TestName "Jira Timeout Configuration" -Status "PASSED" -Details "TimeoutSeconds: $($config.JiraIntegration.TimeoutSeconds)"
        }
    } else {
        Add-TestResult -TestName "Jira Integration Settings" -Status "WARNING" -Details "No specific Jira integration settings found"
    }
    
} catch {
    Add-TestResult -TestName "Configuration Analysis" -Status "FAILED" -Details "Could not load configuration" -ErrorMessage $_.Exception.Message
}

# Test 4: Check Jira Credentials
Write-Host "`n--- Test 4: Jira Credentials Check ---" -ForegroundColor Yellow

$credentialFiles = @("SecureCredentials.xml", "Modules\SecureCredentials.xml")
$jiraCredsFound = $false

foreach ($credFile in $credentialFiles) {
    if (Test-Path $credFile) {
        try {
            $creds = Import-Clixml $credFile
            
            if ($creds.ContainsKey("AdminScript-JiraUsername")) {
                Add-TestResult -TestName "Jira Username ($credFile)" -Status "PASSED" -Details "Jira username credential found"
                $jiraCredsFound = $true
            }
            
            if ($creds.ContainsKey("AdminScript-JiraApiToken")) {
                Add-TestResult -TestName "Jira API Token ($credFile)" -Status "PASSED" -Details "Jira API token credential found"
                $jiraCredsFound = $true
            }
            
        } catch {
            Add-TestResult -TestName "Credential File ($credFile)" -Status "WARNING" -Details "Could not read credential file" -ErrorMessage $_.Exception.Message
        }
    }
}

if (-not $jiraCredsFound) {
    Add-TestResult -TestName "Jira Credentials Overall" -Status "WARNING" -Details "No Jira credentials found in any credential file"
}

# Test 5: Module Loading Simulation
Write-Host "`n--- Test 5: Module Loading Simulation ---" -ForegroundColor Yellow

try {
    # Try to load the Jira module
    Import-Module ".\Modules\JML-Jira.psm1" -Force -ErrorAction Stop
    Add-TestResult -TestName "Jira Module Loading" -Status "PASSED" -Details "Module loaded successfully"
    
    # Check if functions are available
    $loadedFunctions = 0
    foreach ($func in $requiredJiraFunctions) {
        if (Get-Command $func -ErrorAction SilentlyContinue) {
            $loadedFunctions++
        }
    }
    
    Add-TestResult -TestName "Jira Functions Available" -Status "PASSED" -Details "$loadedFunctions of $($requiredJiraFunctions.Count) functions available"
    
    # Test a simple function call (if available)
    if (Get-Command "Format-JiraCommentWiki" -ErrorAction SilentlyContinue) {
        try {
            $testComment = Format-JiraCommentWiki -Message "Test message" -ErrorAction Stop
            Add-TestResult -TestName "Jira Function Test" -Status "PASSED" -Details "Format-JiraCommentWiki function works"
        } catch {
            Add-TestResult -TestName "Jira Function Test" -Status "WARNING" -Details "Function exists but test failed" -ErrorMessage $_.Exception.Message
        }
    }
    
} catch {
    Add-TestResult -TestName "Jira Module Loading" -Status "FAILED" -Details "Could not load Jira module" -ErrorMessage $_.Exception.Message
}

# Test Summary
Write-Host "`n--- Jira Integration Test Summary ---" -ForegroundColor Cyan

$passed = ($testResults | Where-Object { $_.Status -eq "PASSED" }).Count
$failed = ($testResults | Where-Object { $_.Status -eq "FAILED" }).Count
$warnings = ($testResults | Where-Object { $_.Status -eq "WARNING" }).Count

Write-Host "Total Tests: $($testResults.Count)" -ForegroundColor White
Write-Host "Passed: $passed" -ForegroundColor Green
Write-Host "Failed: $failed" -ForegroundColor Red
Write-Host "Warnings: $warnings" -ForegroundColor Yellow

$successRate = if ($testResults.Count -gt 0) {
    ($passed / $testResults.Count) * 100
} else { 0 }

Write-Host "Success Rate: $($successRate.ToString('F1'))%" -ForegroundColor $(if ($successRate -ge 80) { "Green" } elseif ($successRate -ge 60) { "Yellow" } else { "Red" })

# Jira Integration Readiness Assessment
Write-Host "`n--- Jira Integration Readiness Assessment ---" -ForegroundColor Cyan

if ($passed -ge ($testResults.Count * 0.8)) {
    Write-Host "🎉 JIRA INTEGRATION READY FOR TESTING" -ForegroundColor Green
    Write-Host "The Jira integration appears to be properly configured and ready for live testing." -ForegroundColor Green
} elseif ($passed -ge ($testResults.Count * 0.6)) {
    Write-Host "⚠️ JIRA INTEGRATION PARTIALLY READY" -ForegroundColor Yellow
    Write-Host "Some issues detected but core functionality appears available." -ForegroundColor Yellow
} else {
    Write-Host "❌ JIRA INTEGRATION NEEDS ATTENTION" -ForegroundColor Red
    Write-Host "Multiple issues detected that should be resolved before testing." -ForegroundColor Red
}

Write-Host "`n--- Jira Integration Test Complete ---" -ForegroundColor Cyan
