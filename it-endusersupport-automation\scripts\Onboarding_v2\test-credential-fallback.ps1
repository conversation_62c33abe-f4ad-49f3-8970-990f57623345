# Comprehensive test for credential storage fallback mechanism
Write-Host "=== JML Credential Storage Fallback Test ===" -ForegroundColor Cyan
Write-Host ""

# Import required modules
try {
    Import-Module ".\Modules\JML-Security.psm1" -Force -Global -ErrorAction Stop
    Write-Host "✅ JML-Security module loaded successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to load JML-Security module: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Test 1: SecretManagement (Expected to fail in this environment)
Write-Host ""
Write-Host "Test 1: SecretManagement Module" -ForegroundColor Yellow
$secretMgmtAvailable = Get-Module -ListAvailable -Name "Microsoft.PowerShell.SecretManagement" -ErrorAction SilentlyContinue
if ($secretMgmtAvailable) {
    Write-Host "  ✅ SecretManagement module is available" -ForegroundColor Green
    try {
        Import-Module Microsoft.PowerShell.SecretManagement -ErrorAction Stop
        $vaults = Get-SecretVault -ErrorAction SilentlyContinue
        if ($vaults) {
            Write-Host "  ✅ Secret vaults found: $($vaults.Count)" -ForegroundColor Green
        } else {
            Write-Host "  ⚠️ No secret vaults configured" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "  ❌ SecretManagement import failed: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "  ❌ SecretManagement module not available (expected in this environment)" -ForegroundColor Red
}

# Test 2: Windows Credential Manager
Write-Host ""
Write-Host "Test 2: Windows Credential Manager" -ForegroundColor Yellow
try {
    # Try to access Windows Credential Manager
    $credMgrAvailable = Get-Command "Get-StoredCredential" -ErrorAction SilentlyContinue
    if ($credMgrAvailable) {
        Write-Host "  ✅ Credential Manager cmdlets available" -ForegroundColor Green
    } else {
        Write-Host "  ⚠️ Credential Manager cmdlets not available (may need CredentialManager module)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "  ❌ Credential Manager test failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 3: Encrypted File Method (Current working method)
Write-Host ""
Write-Host "Test 3: Encrypted File Method" -ForegroundColor Yellow

# Test main directory credential file
$mainCredFile = ".\SecureCredentials.xml"
if (Test-Path $mainCredFile) {
    Write-Host "  ✅ Main credential file exists: $mainCredFile" -ForegroundColor Green
    try {
        $mainCreds = Import-Clixml -Path $mainCredFile -ErrorAction Stop
        Write-Host "  ✅ Main credential file loaded successfully" -ForegroundColor Green
        Write-Host "    Credentials found: $($mainCreds.Keys -join ', ')" -ForegroundColor Gray
        
        # Test credential retrieval
        if ($mainCreds.ContainsKey("AdminScript-JiraUsername")) {
            $username = $mainCreds["AdminScript-JiraUsername"]
            Write-Host "    ✅ Jira Username: $username" -ForegroundColor Green
        }
        
        if ($mainCreds.ContainsKey("AdminScript-JiraApiToken")) {
            $token = $mainCreds["AdminScript-JiraApiToken"]
            Write-Host "    ✅ Jira API Token: [ENCRYPTED - $(($token.GetType().Name))]" -ForegroundColor Green
        }
    } catch {
        Write-Host "  ❌ Failed to load main credential file: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "  ❌ Main credential file not found: $mainCredFile" -ForegroundColor Red
}

# Test modules directory credential file
$modulesCredFile = ".\Modules\SecureCredentials.xml"
if (Test-Path $modulesCredFile) {
    Write-Host "  ✅ Modules credential file exists: $modulesCredFile" -ForegroundColor Green
    try {
        $modulesCreds = Import-Clixml -Path $modulesCredFile -ErrorAction Stop
        Write-Host "  ✅ Modules credential file loaded successfully" -ForegroundColor Green
        Write-Host "    Credentials found: $($modulesCreds.Keys -join ', ')" -ForegroundColor Gray
    } catch {
        Write-Host "  ❌ Failed to load modules credential file: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "  ❌ Modules credential file not found: $modulesCredFile" -ForegroundColor Red
}

# Test 4: Get-SecureCredential Function
Write-Host ""
Write-Host "Test 4: Get-SecureCredential Function" -ForegroundColor Yellow
try {
    Write-Host "  Testing Jira Username retrieval..." -ForegroundColor Gray
    $jiraUsername = Get-SecureCredential -CredentialName "AdminScript-JiraUsername" -Purpose "Jira username test"
    if ($jiraUsername) {
        Write-Host "  ✅ Jira Username retrieved: $jiraUsername" -ForegroundColor Green
    } else {
        Write-Host "  ❌ Jira Username not retrieved" -ForegroundColor Red
    }
    
    Write-Host "  Testing Jira API Token retrieval..." -ForegroundColor Gray
    $jiraToken = Get-SecureCredential -CredentialName "AdminScript-JiraApiToken" -Purpose "Jira API token test"
    if ($jiraToken) {
        Write-Host "  ✅ Jira API Token retrieved: [SECURE STRING]" -ForegroundColor Green
    } else {
        Write-Host "  ❌ Jira API Token not retrieved" -ForegroundColor Red
    }
} catch {
    Write-Host "  ❌ Get-SecureCredential test failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 5: Configuration File Credential Settings
Write-Host ""
Write-Host "Test 5: Configuration File Credential Settings" -ForegroundColor Yellow
try {
    $config = Import-PowerShellDataFile -Path ".\AdminAccountConfig.psd1" -ErrorAction Stop
    $credSettings = $config.Security.CredentialStorage
    
    Write-Host "  ✅ Configuration loaded successfully" -ForegroundColor Green
    Write-Host "    Primary Method: $($credSettings.PrimaryMethod)" -ForegroundColor Gray
    Write-Host "    Fallback Methods: $($credSettings.FallbackMethods -join ', ')" -ForegroundColor Gray
    Write-Host "    Encrypted File Path: $($credSettings.EncryptedFileSettings.FilePath)" -ForegroundColor Gray
} catch {
    Write-Host "  ❌ Configuration test failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== Credential Storage Test Complete ===" -ForegroundColor Cyan
