# Menu Simulation Test for JML v1.12
# This script tests menu options by examining the script functions directly

Write-Host "=== JML v1.12 Menu Simulation Test ===" -ForegroundColor Cyan
Write-Host "Test Date: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Gray
Write-Host ""

# Test Results Tracking
$testResults = @{
    StartTime = Get-Date
    Tests = @()
    Passed = 0
    Failed = 0
    Warnings = 0
}

function Add-TestResult {
    param(
        [string]$TestName,
        [string]$Status,
        [string]$Details,
        [string]$ErrorMessage = ""
    )
    
    $result = @{
        TestName = $TestName
        Status = $Status
        Details = $Details
        ErrorMessage = $ErrorMessage
        Timestamp = Get-Date
    }
    
    $testResults.Tests += $result
    
    switch ($Status) {
        "PASSED" { 
            $testResults.Passed++
            Write-Host "✅ $TestName - PASSED" -ForegroundColor Green
        }
        "FAILED" { 
            $testResults.Failed++
            Write-Host "❌ $TestName - FAILED" -ForegroundColor Red
        }
        "WARNING" { 
            $testResults.Warnings++
            Write-Host "⚠️ $TestName - WARNING" -ForegroundColor Yellow
        }
    }
    if ($Details) { Write-Host "   $Details" -ForegroundColor Gray }
    if ($ErrorMessage) { Write-Host "   Error: $ErrorMessage" -ForegroundColor Red }
}

# Test 1: Analyze Main Script Functions
Write-Host "`n--- Test 1: Main Script Function Analysis ---" -ForegroundColor Yellow

try {
    $scriptContent = Get-Content "JML_v1.12.ps1" -Raw
    
    # Check for key functions
    $keyFunctions = @(
        "New-AdminAccount",
        "Remove-AdminAccount", 
        "Reset-StdAdminAccount",
        "Show-MainMenu",
        "Show-SystemInformation",
        "Start-JMLSetup"
    )
    
    foreach ($func in $keyFunctions) {
        if ($scriptContent -match "function $func") {
            Add-TestResult -TestName "Function Exists: $func" -Status "PASSED" -Details "Function definition found"
        } else {
            Add-TestResult -TestName "Function Exists: $func" -Status "FAILED" -Details "Function definition not found"
        }
    }
    
} catch {
    Add-TestResult -TestName "Script Analysis" -Status "FAILED" -Details "Could not analyze script" -ErrorMessage $_.Exception.Message
}

# Test 2: Module Function Availability
Write-Host "`n--- Test 2: Module Function Analysis ---" -ForegroundColor Yellow

$moduleTests = @{
    "JML-Configuration" = @("Initialize-SmartConfiguration", "Get-ModuleConfiguration")
    "JML-Security" = @("Get-SecureCredential", "Protect-SensitiveData")
    "JML-Logging" = @("Write-SecureLog", "Initialize-SecureLogging")
    "JML-Utilities" = @("Get-ValidatedUPN", "New-SecurePassword")
    "JML-Email" = @("Send-EmailNotification", "Send-EmailWithRetry")
    "JML-Jira" = @("Initialize-JiraConnection", "Add-EnhancedJiraComment")
}

foreach ($module in $moduleTests.Keys) {
    $modulePath = "Modules\$module.psm1"
    if (Test-Path $modulePath) {
        try {
            $moduleContent = Get-Content $modulePath -Raw
            $functions = $moduleTests[$module]
            
            foreach ($func in $functions) {
                if ($moduleContent -match "function $func") {
                    Add-TestResult -TestName "$module Function: $func" -Status "PASSED" -Details "Function found in module"
                } else {
                    Add-TestResult -TestName "$module Function: $func" -Status "FAILED" -Details "Function not found in module"
                }
            }
        } catch {
            Add-TestResult -TestName "$module Analysis" -Status "FAILED" -Details "Could not analyze module" -ErrorMessage $_.Exception.Message
        }
    } else {
        Add-TestResult -TestName "$module Module" -Status "FAILED" -Details "Module file not found"
    }
}

# Test 3: Configuration Validation
Write-Host "`n--- Test 3: Configuration Validation ---" -ForegroundColor Yellow

try {
    $config = Import-PowerShellDataFile "AdminAccountConfig.psd1"
    
    # Test required configuration keys
    $requiredKeys = @(
        "DefaultDomain",
        "JiraServer", 
        "LogDirectory",
        "DataRedaction",
        "CredentialStorage"
    )
    
    foreach ($key in $requiredKeys) {
        if ($config.ContainsKey($key)) {
            Add-TestResult -TestName "Config Key: $key" -Status "PASSED" -Details "Value: $($config[$key])"
        } else {
            Add-TestResult -TestName "Config Key: $key" -Status "FAILED" -Details "Required key missing"
        }
    }
    
    # Test credential storage configuration
    if ($config.CredentialStorage -and $config.CredentialStorage.FallbackMethods) {
        $fallbackMethods = $config.CredentialStorage.FallbackMethods
        Add-TestResult -TestName "Credential Fallback Methods" -Status "PASSED" -Details "Methods: $($fallbackMethods -join ', ')"
    } else {
        Add-TestResult -TestName "Credential Fallback Methods" -Status "WARNING" -Details "Fallback methods not configured"
    }
    
} catch {
    Add-TestResult -TestName "Configuration Validation" -Status "FAILED" -Details "Could not validate configuration" -ErrorMessage $_.Exception.Message
}

# Test 4: Credential Storage Test
Write-Host "`n--- Test 4: Credential Storage Test ---" -ForegroundColor Yellow

$credentialFiles = @("SecureCredentials.xml", "Modules\SecureCredentials.xml")
$credentialsWorking = $false

foreach ($credFile in $credentialFiles) {
    if (Test-Path $credFile) {
        try {
            $creds = Import-Clixml $credFile
            if ($creds -and $creds.Count -gt 0) {
                Add-TestResult -TestName "Credential File: $credFile" -Status "PASSED" -Details "Contains $($creds.Count) credential entries"
                $credentialsWorking = $true
                
                # Check for specific credentials
                if ($creds.ContainsKey("AdminScript-JiraUsername")) {
                    Add-TestResult -TestName "Jira Username Credential" -Status "PASSED" -Details "Jira username credential found"
                }
                if ($creds.ContainsKey("AdminScript-JiraApiToken")) {
                    Add-TestResult -TestName "Jira API Token Credential" -Status "PASSED" -Details "Jira API token credential found"
                }
            } else {
                Add-TestResult -TestName "Credential File: $credFile" -Status "WARNING" -Details "File exists but contains no credentials"
            }
        } catch {
            Add-TestResult -TestName "Credential File: $credFile" -Status "WARNING" -Details "File exists but cannot be read" -ErrorMessage $_.Exception.Message
        }
    }
}

if (-not $credentialsWorking) {
    Add-TestResult -TestName "Overall Credential Storage" -Status "WARNING" -Details "No working credential files found"
}

# Test 5: Jira Integration Readiness
Write-Host "`n--- Test 5: Jira Integration Readiness ---" -ForegroundColor Yellow

try {
    # Check if Jira module exists and has required functions
    if (Test-Path "Modules\JML-Jira.psm1") {
        $jiraContent = Get-Content "Modules\JML-Jira.psm1" -Raw
        
        $jiraFunctions = @(
            "Initialize-JiraConnection",
            "Test-JiraTicketValidation", 
            "Add-EnhancedJiraComment",
            "Add-EnhancedJiraAttachment"
        )
        
        $jiraFunctionsFound = 0
        foreach ($func in $jiraFunctions) {
            if ($jiraContent -match "function $func") {
                $jiraFunctionsFound++
            }
        }
        
        if ($jiraFunctionsFound -eq $jiraFunctions.Count) {
            Add-TestResult -TestName "Jira Integration Functions" -Status "PASSED" -Details "All $($jiraFunctions.Count) required functions found"
        } else {
            Add-TestResult -TestName "Jira Integration Functions" -Status "WARNING" -Details "Only $jiraFunctionsFound of $($jiraFunctions.Count) functions found"
        }
        
        # Check for test ticket configuration
        $config = Import-PowerShellDataFile "AdminAccountConfig.psd1"
        if ($config.TestTicket) {
            Add-TestResult -TestName "Jira Test Ticket" -Status "PASSED" -Details "Test ticket configured: $($config.TestTicket)"
        } else {
            Add-TestResult -TestName "Jira Test Ticket" -Status "WARNING" -Details "No test ticket configured"
        }
        
    } else {
        Add-TestResult -TestName "Jira Module" -Status "FAILED" -Details "JML-Jira.psm1 module not found"
    }
} catch {
    Add-TestResult -TestName "Jira Integration Analysis" -Status "FAILED" -Details "Could not analyze Jira integration" -ErrorMessage $_.Exception.Message
}

# Display Results Summary
Write-Host "`n--- Test Results Summary ---" -ForegroundColor Cyan
Write-Host "Total Tests: $($testResults.Tests.Count)" -ForegroundColor White
Write-Host "Passed: $($testResults.Passed)" -ForegroundColor Green
Write-Host "Failed: $($testResults.Failed)" -ForegroundColor Red
Write-Host "Warnings: $($testResults.Warnings)" -ForegroundColor Yellow

$duration = (Get-Date) - $testResults.StartTime
Write-Host "Test Duration: $($duration.TotalSeconds.ToString('F2')) seconds" -ForegroundColor Gray

$successRate = if ($testResults.Tests.Count -gt 0) {
    ($testResults.Passed / $testResults.Tests.Count) * 100
} else { 0 }

Write-Host "Success Rate: $($successRate.ToString('F1'))%" -ForegroundColor $(if ($successRate -ge 80) { "Green" } elseif ($successRate -ge 60) { "Yellow" } else { "Red" })

Write-Host "`n--- Menu Simulation Test Complete ---" -ForegroundColor Cyan
