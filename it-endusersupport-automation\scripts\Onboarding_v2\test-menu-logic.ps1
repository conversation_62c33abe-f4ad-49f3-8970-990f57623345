# Menu Logic Test for JML v1.12
# This script tests the menu system logic and option handling

Write-Host "=== JML v1.12 Menu Logic Test ===" -ForegroundColor Cyan
Write-Host "Test Date: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Gray
Write-Host ""

# Test Results
$testResults = @()

function Add-TestResult {
    param($TestName, $Status, $Details, $ErrorMessage = "")
    
    $result = @{
        TestName = $TestName
        Status = $Status
        Details = $Details
        ErrorMessage = $ErrorMessage
        Timestamp = Get-Date
    }
    
    $script:testResults += $result
    
    $color = switch ($Status) {
        "PASSED" { "Green" }
        "FAILED" { "Red" }
        "WARNING" { "Yellow" }
    }
    
    Write-Host "$(if($Status -eq 'PASSED'){'✅'}elseif($Status -eq 'FAILED'){'❌'}else{'⚠️'}) $TestName - $Status" -ForegroundColor $color
    if ($Details) { Write-Host "   $Details" -ForegroundColor Gray }
    if ($ErrorMessage) { Write-Host "   Error: $ErrorMessage" -ForegroundColor Red }
}

# Test 1: Menu Display Function
Write-Host "`n--- Test 1: Menu Display Function ---" -ForegroundColor Yellow

try {
    $scriptContent = Get-Content "JML_v1.12.ps1" -Raw
    
    if ($scriptContent -match "function Show-MainMenu") {
        Add-TestResult -TestName "Show-MainMenu Function" -Status "PASSED" -Details "Function definition found"
        
        # Check for menu options
        $menuOptions = @(
            "1.*Create Admin Account",
            "2.*Delete Admin Account", 
            "3.*Reset Admin Account Password",
            "4.*System Information",
            "5.*Run Setup",
            "6.*Exit"
        )
        
        foreach ($option in $menuOptions) {
            if ($scriptContent -match $option) {
                Add-TestResult -TestName "Menu Option: $($option.Split('.*')[1])" -Status "PASSED" -Details "Option text found"
            } else {
                Add-TestResult -TestName "Menu Option: $($option.Split('.*')[1])" -Status "WARNING" -Details "Option text not found or different format"
            }
        }
    } else {
        Add-TestResult -TestName "Show-MainMenu Function" -Status "FAILED" -Details "Function definition not found"
    }
} catch {
    Add-TestResult -TestName "Menu Display Analysis" -Status "FAILED" -Details "Could not analyze menu display" -ErrorMessage $_.Exception.Message
}

# Test 2: Menu Choice Handling
Write-Host "`n--- Test 2: Menu Choice Handling ---" -ForegroundColor Yellow

try {
    $scriptContent = Get-Content "JML_v1.12.ps1" -Raw
    
    # Check for switch statement or choice handling
    if ($scriptContent -match "switch.*choice|if.*choice") {
        Add-TestResult -TestName "Menu Choice Handling" -Status "PASSED" -Details "Choice handling logic found"
        
        # Check for specific choice handlers
        $choiceHandlers = @(
            '"1".*New-AdminAccount',
            '"2".*Remove-StdAdminAccount',
            '"3".*Reset-StdAdminAccount',
            '"4".*Show-SystemInformation',
            '"5".*Start-JMLSetup',
            '"6".*Exit'
        )
        
        foreach ($handler in $choiceHandlers) {
            if ($scriptContent -match $handler) {
                Add-TestResult -TestName "Choice Handler: $($handler.Split('.*')[0])" -Status "PASSED" -Details "Handler found"
            } else {
                Add-TestResult -TestName "Choice Handler: $($handler.Split('.*')[0])" -Status "WARNING" -Details "Handler not found or different pattern"
            }
        }
    } else {
        Add-TestResult -TestName "Menu Choice Handling" -Status "FAILED" -Details "Choice handling logic not found"
    }
} catch {
    Add-TestResult -TestName "Menu Choice Analysis" -Status "FAILED" -Details "Could not analyze menu choices" -ErrorMessage $_.Exception.Message
}

# Test 3: Input Validation
Write-Host "`n--- Test 3: Input Validation ---" -ForegroundColor Yellow

try {
    $scriptContent = Get-Content "JML_v1.12.ps1" -Raw
    
    # Check for input validation
    $validationChecks = @(
        @{Name = "Invalid Choice Handling"; Pattern = "Invalid choice|default.*Invalid"},
        @{Name = "Input Range Validation"; Pattern = "1-6|between.*1.*6"},
        @{Name = "User Input Reading"; Pattern = "Read-Host.*choice"},
        @{Name = "Continue Prompt"; Pattern = "Press any key.*continue"}
    )
    
    foreach ($check in $validationChecks) {
        if ($scriptContent -match $check.Pattern) {
            Add-TestResult -TestName "Input Validation: $($check.Name)" -Status "PASSED" -Details "Validation found"
        } else {
            Add-TestResult -TestName "Input Validation: $($check.Name)" -Status "WARNING" -Details "Validation not found"
        }
    }
} catch {
    Add-TestResult -TestName "Input Validation Analysis" -Status "FAILED" -Details "Could not analyze input validation" -ErrorMessage $_.Exception.Message
}

# Test 4: Menu Loop Logic
Write-Host "`n--- Test 4: Menu Loop Logic ---" -ForegroundColor Yellow

try {
    $scriptContent = Get-Content "JML_v1.12.ps1" -Raw
    
    # Check for menu loop
    if ($scriptContent -match "do.*while.*true|while.*true.*Show-MainMenu") {
        Add-TestResult -TestName "Menu Loop Logic" -Status "PASSED" -Details "Menu loop found"
    } else {
        Add-TestResult -TestName "Menu Loop Logic" -Status "WARNING" -Details "Menu loop not found or different pattern"
    }
    
    # Check for exit condition
    if ($scriptContent -match "return.*6|exit.*6|break.*6") {
        Add-TestResult -TestName "Exit Condition" -Status "PASSED" -Details "Exit condition found"
    } else {
        Add-TestResult -TestName "Exit Condition" -Status "WARNING" -Details "Exit condition not found"
    }
    
} catch {
    Add-TestResult -TestName "Menu Loop Analysis" -Status "FAILED" -Details "Could not analyze menu loop" -ErrorMessage $_.Exception.Message
}

# Test 5: User Experience Features
Write-Host "`n--- Test 5: User Experience Features ---" -ForegroundColor Yellow

try {
    $scriptContent = Get-Content "JML_v1.12.ps1" -Raw
    
    # Check for UX features
    $uxFeatures = @(
        @{Name = "Clear Screen"; Pattern = "Clear-Host"},
        @{Name = "Color Coding"; Pattern = "-ForegroundColor"},
        @{Name = "Progress Indicators"; Pattern = "Write-Progress"},
        @{Name = "User Information Display"; Pattern = "Current User.*Computer"},
        @{Name = "System Status Display"; Pattern = "Configuration.*Loaded|Jira Integration.*Enabled"}
    )
    
    foreach ($feature in $uxFeatures) {
        $matches = [regex]::Matches($scriptContent, $feature.Pattern, [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
        if ($matches.Count -gt 0) {
            Add-TestResult -TestName "UX Feature: $($feature.Name)" -Status "PASSED" -Details "$($matches.Count) instances found"
        } else {
            Add-TestResult -TestName "UX Feature: $($feature.Name)" -Status "WARNING" -Details "Feature not found"
        }
    }
} catch {
    Add-TestResult -TestName "UX Features Analysis" -Status "FAILED" -Details "Could not analyze UX features" -ErrorMessage $_.Exception.Message
}

# Test 6: Error Handling in Menu System
Write-Host "`n--- Test 6: Error Handling in Menu System ---" -ForegroundColor Yellow

try {
    $scriptContent = Get-Content "JML_v1.12.ps1" -Raw
    
    # Check for error handling in menu system
    $errorHandlingChecks = @(
        @{Name = "Try-Catch in Main Loop"; Pattern = "try.*Show-MainMenu.*catch"},
        @{Name = "Error Messages"; Pattern = "Write-Error.*menu|Write-Host.*Error"},
        @{Name = "Graceful Failure"; Pattern = "continue.*error|return.*error"},
        @{Name = "User Notification"; Pattern = "Write-Host.*failed|Write-Host.*error"}
    )
    
    foreach ($check in $errorHandlingChecks) {
        if ($scriptContent -match $check.Pattern) {
            Add-TestResult -TestName "Menu Error Handling: $($check.Name)" -Status "PASSED" -Details "Error handling found"
        } else {
            Add-TestResult -TestName "Menu Error Handling: $($check.Name)" -Status "WARNING" -Details "Error handling not found"
        }
    }
} catch {
    Add-TestResult -TestName "Menu Error Handling Analysis" -Status "FAILED" -Details "Could not analyze error handling" -ErrorMessage $_.Exception.Message
}

# Test Summary
Write-Host "`n--- Menu Logic Test Summary ---" -ForegroundColor Cyan

$passed = ($testResults | Where-Object { $_.Status -eq "PASSED" }).Count
$failed = ($testResults | Where-Object { $_.Status -eq "FAILED" }).Count
$warnings = ($testResults | Where-Object { $_.Status -eq "WARNING" }).Count

Write-Host "Total Tests: $($testResults.Count)" -ForegroundColor White
Write-Host "Passed: $passed" -ForegroundColor Green
Write-Host "Failed: $failed" -ForegroundColor Red
Write-Host "Warnings: $warnings" -ForegroundColor Yellow

$successRate = if ($testResults.Count -gt 0) {
    ($passed / $testResults.Count) * 100
} else { 0 }

Write-Host "Success Rate: $($successRate.ToString('F1'))%" -ForegroundColor $(if ($successRate -ge 80) { "Green" } elseif ($successRate -ge 60) { "Yellow" } else { "Red" })

# Menu System Readiness Assessment
Write-Host "`n--- Menu System Readiness Assessment ---" -ForegroundColor Cyan

if ($passed -ge ($testResults.Count * 0.8)) {
    Write-Host "🎉 MENU SYSTEM READY FOR PRODUCTION" -ForegroundColor Green
    Write-Host "The menu system appears to be fully functional and ready for user interaction." -ForegroundColor Green
} elseif ($passed -ge ($testResults.Count * 0.6)) {
    Write-Host "⚠️ MENU SYSTEM PARTIALLY READY" -ForegroundColor Yellow
    Write-Host "Some issues detected but core menu functionality appears available." -ForegroundColor Yellow
} else {
    Write-Host "❌ MENU SYSTEM NEEDS ATTENTION" -ForegroundColor Red
    Write-Host "Multiple issues detected that should be resolved before deployment." -ForegroundColor Red
}

Write-Host "`n--- Menu Logic Test Complete ---" -ForegroundColor Cyan
