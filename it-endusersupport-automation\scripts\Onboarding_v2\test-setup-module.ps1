# Test JML-Setup Module
# This script tests the JML-Setup module for syntax errors and functionality

Write-Host "=== JML-Setup Module Test ===" -ForegroundColor Cyan
Write-Host "Test Date: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Gray
Write-Host ""

# Test Results
$testResults = @()

function Add-TestResult {
    param($TestName, $Status, $Details, $ErrorMessage = "")
    
    $result = @{
        TestName = $TestName
        Status = $Status
        Details = $Details
        ErrorMessage = $ErrorMessage
        Timestamp = Get-Date
    }
    
    $script:testResults += $result
    
    $color = switch ($Status) {
        "PASSED" { "Green" }
        "FAILED" { "Red" }
        "WARNING" { "Yellow" }
    }
    
    Write-Host "$(if($Status -eq 'PASSED'){'✅'}elseif($Status -eq 'FAILED'){'❌'}else{'⚠️'}) $TestName - $Status" -ForegroundColor $color
    if ($Details) { Write-Host "   $Details" -ForegroundColor Gray }
    if ($ErrorMessage) { Write-Host "   Error: $ErrorMessage" -ForegroundColor Red }
}

# Test 1: Module File Existence
Write-Host "`n--- Test 1: Module File Existence ---" -ForegroundColor Yellow

if (Test-Path "Modules\JML-Setup.psm1") {
    Add-TestResult -TestName "JML-Setup Module File" -Status "PASSED" -Details "Module file found"
} else {
    Add-TestResult -TestName "JML-Setup Module File" -Status "FAILED" -Details "Module file not found"
    Write-Host "Cannot continue testing without module file." -ForegroundColor Red
    exit 1
}

# Test 2: Syntax Validation
Write-Host "`n--- Test 2: Syntax Validation ---" -ForegroundColor Yellow

try {
    $moduleContent = Get-Content "Modules\JML-Setup.psm1" -Raw
    
    # Try to parse the module for syntax errors
    $null = [System.Management.Automation.PSParser]::Tokenize($moduleContent, [ref]$null)
    Add-TestResult -TestName "Module Syntax Check" -Status "PASSED" -Details "No syntax errors detected"
    
    # Check for required elements
    if ($moduleContent -match "Export-ModuleMember") {
        Add-TestResult -TestName "Export Statement" -Status "PASSED" -Details "Export-ModuleMember found"
    } else {
        Add-TestResult -TestName "Export Statement" -Status "FAILED" -Details "Export-ModuleMember not found"
    }
    
    # Check for required functions
    $requiredFunctions = @("Start-JMLSetup", "Test-Environment", "Write-SetupMessage")
    foreach ($func in $requiredFunctions) {
        if ($moduleContent -match "function $func") {
            Add-TestResult -TestName "Function: $func" -Status "PASSED" -Details "Function definition found"
        } else {
            Add-TestResult -TestName "Function: $func" -Status "FAILED" -Details "Function definition not found"
        }
    }
    
} catch {
    Add-TestResult -TestName "Module Syntax Check" -Status "FAILED" -Details "Syntax error detected" -ErrorMessage $_.Exception.Message
}

# Test 3: Module Loading
Write-Host "`n--- Test 3: Module Loading ---" -ForegroundColor Yellow

try {
    # Remove module if already loaded
    if (Get-Module -Name "JML-Setup" -ErrorAction SilentlyContinue) {
        Remove-Module -Name "JML-Setup" -Force
    }
    
    # Try to import the module
    Import-Module ".\Modules\JML-Setup.psm1" -Force -ErrorAction Stop
    Add-TestResult -TestName "Module Import" -Status "PASSED" -Details "Module imported successfully"
    
    # Check if functions are available
    $loadedModule = Get-Module -Name "JML-Setup"
    if ($loadedModule) {
        Add-TestResult -TestName "Module Loaded" -Status "PASSED" -Details "Module found in loaded modules list"
        Add-TestResult -TestName "Exported Functions" -Status "PASSED" -Details "Functions exported: $($loadedModule.ExportedFunctions.Count)"
        
        # List exported functions
        if ($loadedModule.ExportedFunctions.Count -gt 0) {
            $functionNames = $loadedModule.ExportedFunctions.Keys -join ", "
            Add-TestResult -TestName "Function Names" -Status "PASSED" -Details $functionNames
        }
    } else {
        Add-TestResult -TestName "Module Loaded" -Status "FAILED" -Details "Module not found in loaded modules list"
    }
    
} catch {
    Add-TestResult -TestName "Module Import" -Status "FAILED" -Details "Failed to import module" -ErrorMessage $_.Exception.Message
}

# Test 4: Function Availability
Write-Host "`n--- Test 4: Function Availability ---" -ForegroundColor Yellow

$testFunctions = @("Start-JMLSetup", "Test-Environment", "Write-SetupMessage", "Test-ModuleAvailability")

foreach ($func in $testFunctions) {
    try {
        $command = Get-Command $func -ErrorAction Stop
        Add-TestResult -TestName "Function Available: $func" -Status "PASSED" -Details "Function is available for use"
    } catch {
        Add-TestResult -TestName "Function Available: $func" -Status "FAILED" -Details "Function not available" -ErrorMessage $_.Exception.Message
    }
}

# Test 5: Basic Function Testing
Write-Host "`n--- Test 5: Basic Function Testing ---" -ForegroundColor Yellow

# Test Write-SetupMessage
try {
    if (Get-Command "Write-SetupMessage" -ErrorAction SilentlyContinue) {
        Write-SetupMessage -Message "Test message" -Type "Information"
        Add-TestResult -TestName "Write-SetupMessage Test" -Status "PASSED" -Details "Function executed without error"
    } else {
        Add-TestResult -TestName "Write-SetupMessage Test" -Status "FAILED" -Details "Function not available"
    }
} catch {
    Add-TestResult -TestName "Write-SetupMessage Test" -Status "FAILED" -Details "Function execution failed" -ErrorMessage $_.Exception.Message
}

# Test Test-Environment
try {
    if (Get-Command "Test-Environment" -ErrorAction SilentlyContinue) {
        $envResult = Test-Environment
        Add-TestResult -TestName "Test-Environment Test" -Status "PASSED" -Details "Function executed, result: $envResult"
    } else {
        Add-TestResult -TestName "Test-Environment Test" -Status "FAILED" -Details "Function not available"
    }
} catch {
    Add-TestResult -TestName "Test-Environment Test" -Status "FAILED" -Details "Function execution failed" -ErrorMessage $_.Exception.Message
}

# Test Start-JMLSetup
try {
    if (Get-Command "Start-JMLSetup" -ErrorAction SilentlyContinue) {
        $setupResult = Start-JMLSetup -ValidateEnvironment
        Add-TestResult -TestName "Start-JMLSetup Test" -Status "PASSED" -Details "Function executed, result: $setupResult"
    } else {
        Add-TestResult -TestName "Start-JMLSetup Test" -Status "FAILED" -Details "Function not available"
    }
} catch {
    Add-TestResult -TestName "Start-JMLSetup Test" -Status "FAILED" -Details "Function execution failed" -ErrorMessage $_.Exception.Message
}

# Test Summary
Write-Host "`n--- JML-Setup Module Test Summary ---" -ForegroundColor Cyan

$passed = ($testResults | Where-Object { $_.Status -eq "PASSED" }).Count
$failed = ($testResults | Where-Object { $_.Status -eq "FAILED" }).Count
$warnings = ($testResults | Where-Object { $_.Status -eq "WARNING" }).Count

Write-Host "Total Tests: $($testResults.Count)" -ForegroundColor White
Write-Host "Passed: $passed" -ForegroundColor Green
Write-Host "Failed: $failed" -ForegroundColor Red
Write-Host "Warnings: $warnings" -ForegroundColor Yellow

$successRate = if ($testResults.Count -gt 0) {
    ($passed / $testResults.Count) * 100
} else { 0 }

Write-Host "Success Rate: $($successRate.ToString('F1'))%" -ForegroundColor $(if ($successRate -ge 80) { "Green" } elseif ($successRate -ge 60) { "Yellow" } else { "Red" })

# Module Readiness Assessment
Write-Host "`n--- JML-Setup Module Assessment ---" -ForegroundColor Cyan

if ($passed -ge ($testResults.Count * 0.8)) {
    Write-Host "🎉 JML-SETUP MODULE IS WORKING CORRECTLY" -ForegroundColor Green
    Write-Host "The module appears to be syntactically correct and functional." -ForegroundColor Green
} elseif ($passed -ge ($testResults.Count * 0.6)) {
    Write-Host "⚠️ JML-SETUP MODULE HAS MINOR ISSUES" -ForegroundColor Yellow
    Write-Host "Some issues detected but core functionality appears available." -ForegroundColor Yellow
} else {
    Write-Host "❌ JML-SETUP MODULE NEEDS ATTENTION" -ForegroundColor Red
    Write-Host "Multiple issues detected that should be resolved." -ForegroundColor Red
}

Write-Host "`n--- JML-Setup Module Test Complete ---" -ForegroundColor Cyan
