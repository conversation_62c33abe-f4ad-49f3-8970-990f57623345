# Test script to run J<PERSON> in interactive mode with timeout
Write-Host "=== JML Interactive Mode Test ===" -ForegroundColor Cyan
Write-Host ""

# Create a job to run the script with timeout
$scriptBlock = {
    try {
        Set-Location "C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\Onboarding_v2"
        Write-Host "Starting JML script..." -ForegroundColor Yellow
        
        # Start the script
        & ".\JML_v1.12.ps1"
    } catch {
        Write-Host "Script error: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

Write-Host "Starting JML script as background job with 30-second timeout..." -ForegroundColor Yellow
$job = Start-Job -ScriptBlock $scriptBlock

# Wait for job completion or timeout
$timeout = 30
$completed = Wait-Job -Job $job -Timeout $timeout

if ($completed) {
    Write-Host "Script completed within timeout" -ForegroundColor Green
    $output = Receive-Job -Job $job
    Write-Host "Output:" -ForegroundColor Cyan
    Write-Host $output
} else {
    Write-Host "Script did not complete within $timeout seconds - likely hanging" -ForegroundColor Red
    Write-Host "Stopping job..." -ForegroundColor Yellow
    Stop-Job -Job $job
    
    # Get partial output
    $output = Receive-Job -Job $job
    if ($output) {
        Write-Host "Partial output before hang:" -ForegroundColor Yellow
        Write-Host $output
    }
}

# Cleanup
Remove-Job -Job $job -Force

Write-Host ""
Write-Host "=== Test Complete ===" -ForegroundColor Cyan
