# End-to-End Workflow Test for JML v1.12
# This script tests complete workflows from start to finish

Write-Host "=== JML v1.12 End-to-End Workflow Test ===" -ForegroundColor Cyan
Write-Host "Test Date: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Gray
Write-Host ""

# Test Results
$testResults = @()

function Add-TestResult {
    param($TestName, $Status, $Details, $ErrorMessage = "")
    
    $result = @{
        TestName = $TestName
        Status = $Status
        Details = $Details
        ErrorMessage = $ErrorMessage
        Timestamp = Get-Date
    }
    
    $script:testResults += $result
    
    $color = switch ($Status) {
        "PASSED" { "Green" }
        "FAILED" { "Red" }
        "WARNING" { "Yellow" }
    }
    
    Write-Host "$(if($Status -eq 'PASSED'){'✅'}elseif($Status -eq 'FAILED'){'❌'}else{'⚠️'}) $TestName - $Status" -ForegroundColor $color
    if ($Details) { Write-Host "   $Details" -ForegroundColor Gray }
    if ($ErrorMessage) { Write-Host "   Error: $ErrorMessage" -ForegroundColor Red }
}

# Test 1: Complete System Initialization
Write-Host "`n--- Test 1: Complete System Initialization ---" -ForegroundColor Yellow

try {
    # Check all required files
    $requiredFiles = @(
        "JML_v1.12.ps1",
        "AdminAccountConfig.psd1",
        "Modules\JML-Configuration.psm1",
        "Modules\JML-Security.psm1",
        "Modules\JML-Logging.psm1",
        "Modules\JML-Utilities.psm1",
        "Modules\JML-Email.psm1",
        "Modules\JML-Jira.psm1"
    )
    
    $missingFiles = @()
    foreach ($file in $requiredFiles) {
        if (-not (Test-Path $file)) {
            $missingFiles += $file
        }
    }
    
    if ($missingFiles.Count -eq 0) {
        Add-TestResult -TestName "System Files Check" -Status "PASSED" -Details "All required files present"
    } else {
        Add-TestResult -TestName "System Files Check" -Status "FAILED" -Details "Missing files: $($missingFiles -join ', ')"
    }
    
} catch {
    Add-TestResult -TestName "System Files Check" -Status "FAILED" -Details "Could not check system files" -ErrorMessage $_.Exception.Message
}

# Test 2: Configuration Integrity
Write-Host "`n--- Test 2: Configuration Integrity ---" -ForegroundColor Yellow

try {
    $config = Import-PowerShellDataFile "AdminAccountConfig.psd1"
    
    # Check critical configuration sections
    $criticalSections = @("ScriptSettings", "Logging", "ActiveDirectory", "Email", "Jira", "Security")
    $missingSections = @()
    
    foreach ($section in $criticalSections) {
        if (-not $config.ContainsKey($section)) {
            $missingSections += $section
        }
    }
    
    if ($missingSections.Count -eq 0) {
        Add-TestResult -TestName "Configuration Integrity" -Status "PASSED" -Details "All critical sections present"
    } else {
        Add-TestResult -TestName "Configuration Integrity" -Status "FAILED" -Details "Missing sections: $($missingSections -join ', ')"
    }
    
    # Check specific critical settings
    if ($config.ScriptSettings.DefaultDomain) {
        Add-TestResult -TestName "Default Domain Setting" -Status "PASSED" -Details $config.ScriptSettings.DefaultDomain
    } else {
        Add-TestResult -TestName "Default Domain Setting" -Status "FAILED" -Details "Default domain not configured"
    }
    
    if ($config.Jira.ServerUrl) {
        Add-TestResult -TestName "Jira Server Setting" -Status "PASSED" -Details $config.Jira.ServerUrl
    } else {
        Add-TestResult -TestName "Jira Server Setting" -Status "FAILED" -Details "Jira server not configured"
    }
    
} catch {
    Add-TestResult -TestName "Configuration Integrity" -Status "FAILED" -Details "Could not load configuration" -ErrorMessage $_.Exception.Message
}

# Test 3: Credential System Workflow
Write-Host "`n--- Test 3: Credential System Workflow ---" -ForegroundColor Yellow

try {
    # Check credential files
    $credentialFiles = @("SecureCredentials.xml", "Modules\SecureCredentials.xml")
    $workingCredFiles = 0
    
    foreach ($credFile in $credentialFiles) {
        if (Test-Path $credFile) {
            try {
                $creds = Import-Clixml $credFile
                if ($creds -and $creds.Count -gt 0) {
                    $workingCredFiles++
                    Add-TestResult -TestName "Credential File: $credFile" -Status "PASSED" -Details "Contains $($creds.Count) credentials"
                    
                    # Check for required credentials
                    if ($creds.ContainsKey("AdminScript-JiraUsername") -and $creds.ContainsKey("AdminScript-JiraApiToken")) {
                        Add-TestResult -TestName "Jira Credentials in $credFile" -Status "PASSED" -Details "Both username and API token found"
                    } else {
                        Add-TestResult -TestName "Jira Credentials in $credFile" -Status "WARNING" -Details "Missing Jira credentials"
                    }
                }
            } catch {
                Add-TestResult -TestName "Credential File: $credFile" -Status "WARNING" -Details "File exists but cannot be read"
            }
        }
    }
    
    if ($workingCredFiles -gt 0) {
        Add-TestResult -TestName "Credential System Overall" -Status "PASSED" -Details "$workingCredFiles working credential files found"
    } else {
        Add-TestResult -TestName "Credential System Overall" -Status "FAILED" -Details "No working credential files found"
    }
    
} catch {
    Add-TestResult -TestName "Credential System Workflow" -Status "FAILED" -Details "Could not test credential system" -ErrorMessage $_.Exception.Message
}

# Test 4: Module Integration Workflow
Write-Host "`n--- Test 4: Module Integration Workflow ---" -ForegroundColor Yellow

try {
    # Test module loading sequence
    $moduleLoadOrder = @(
        "JML-Configuration",
        "JML-Security", 
        "JML-Logging",
        "JML-Utilities",
        "JML-Email",
        "JML-Jira"
    )
    
    $loadedModules = 0
    foreach ($moduleName in $moduleLoadOrder) {
        $modulePath = ".\Modules\$moduleName.psm1"
        if (Test-Path $modulePath) {
            try {
                # Remove if already loaded
                if (Get-Module -Name $moduleName -ErrorAction SilentlyContinue) {
                    Remove-Module -Name $moduleName -Force
                }
                
                Import-Module $modulePath -Force -ErrorAction Stop
                $loadedModules++
                Add-TestResult -TestName "Module Load: $moduleName" -Status "PASSED" -Details "Module loaded successfully"
            } catch {
                Add-TestResult -TestName "Module Load: $moduleName" -Status "WARNING" -Details "Module failed to load" -ErrorMessage $_.Exception.Message
            }
        } else {
            Add-TestResult -TestName "Module Load: $moduleName" -Status "FAILED" -Details "Module file not found"
        }
    }
    
    Add-TestResult -TestName "Module Integration Overall" -Status "PASSED" -Details "$loadedModules of $($moduleLoadOrder.Count) modules loaded"
    
} catch {
    Add-TestResult -TestName "Module Integration Workflow" -Status "FAILED" -Details "Could not test module integration" -ErrorMessage $_.Exception.Message
}

# Test 5: Function Availability Workflow
Write-Host "`n--- Test 5: Function Availability Workflow ---" -ForegroundColor Yellow

try {
    # Test critical functions from each module
    $criticalFunctions = @(
        @{Module = "JML-Configuration"; Function = "Initialize-SmartConfiguration"},
        @{Module = "JML-Security"; Function = "Get-SecureCredential"},
        @{Module = "JML-Logging"; Function = "Write-SecureLog"},
        @{Module = "JML-Utilities"; Function = "Get-ValidatedUPN"},
        @{Module = "JML-Email"; Function = "Send-EmailNotification"},
        @{Module = "JML-Jira"; Function = "Initialize-JiraConnection"}
    )
    
    $availableFunctions = 0
    foreach ($funcTest in $criticalFunctions) {
        try {
            $command = Get-Command $funcTest.Function -ErrorAction Stop
            $availableFunctions++
            Add-TestResult -TestName "Function: $($funcTest.Function)" -Status "PASSED" -Details "Function available from $($funcTest.Module)"
        } catch {
            Add-TestResult -TestName "Function: $($funcTest.Function)" -Status "WARNING" -Details "Function not available from $($funcTest.Module)"
        }
    }
    
    Add-TestResult -TestName "Function Availability Overall" -Status "PASSED" -Details "$availableFunctions of $($criticalFunctions.Count) critical functions available"
    
} catch {
    Add-TestResult -TestName "Function Availability Workflow" -Status "FAILED" -Details "Could not test function availability" -ErrorMessage $_.Exception.Message
}

# Test 6: Admin Function Workflow Simulation
Write-Host "`n--- Test 6: Admin Function Workflow Simulation ---" -ForegroundColor Yellow

try {
    $scriptContent = Get-Content "JML_v1.12.ps1" -Raw
    
    # Test admin function workflow components
    $workflowComponents = @(
        @{Name = "Ticket Key Validation"; Pattern = "ValidatePattern.*\^\[A-Z\]\+.*\d\+"},
        @{Name = "Jira Integration Call"; Pattern = "Initialize-JiraConnection.*Test-JiraTicketValidation"},
        @{Name = "Credential Retrieval"; Pattern = "Get-SecureCredential.*JiraApiToken"},
        @{Name = "Email Notification"; Pattern = "Send-EmailNotification|Send-DeletionEmailNotification|Send-ResetEmailNotification"},
        @{Name = "Secure Logging"; Pattern = "Write-SecureLog.*AuditTrail"},
        @{Name = "Error Handling"; Pattern = "try.*catch.*Write-Error"}
    )
    
    foreach ($component in $workflowComponents) {
        if ($scriptContent -match $component.Pattern) {
            Add-TestResult -TestName "Workflow: $($component.Name)" -Status "PASSED" -Details "Component found in admin functions"
        } else {
            Add-TestResult -TestName "Workflow: $($component.Name)" -Status "WARNING" -Details "Component not found or different pattern"
        }
    }
    
} catch {
    Add-TestResult -TestName "Admin Function Workflow Simulation" -Status "FAILED" -Details "Could not simulate workflow" -ErrorMessage $_.Exception.Message
}

# Test 7: System Readiness Assessment
Write-Host "`n--- Test 7: System Readiness Assessment ---" -ForegroundColor Yellow

try {
    # Calculate overall system readiness
    $totalTests = $testResults.Count
    $passedTests = ($testResults | Where-Object { $_.Status -eq "PASSED" }).Count
    $failedTests = ($testResults | Where-Object { $_.Status -eq "FAILED" }).Count
    $warningTests = ($testResults | Where-Object { $_.Status -eq "WARNING" }).Count
    
    $readinessScore = if ($totalTests -gt 0) {
        (($passedTests * 1.0) + ($warningTests * 0.5)) / $totalTests * 100
    } else { 0 }
    
    if ($readinessScore -ge 90) {
        Add-TestResult -TestName "System Readiness" -Status "PASSED" -Details "Excellent readiness: $($readinessScore.ToString('F1'))%"
    } elseif ($readinessScore -ge 75) {
        Add-TestResult -TestName "System Readiness" -Status "PASSED" -Details "Good readiness: $($readinessScore.ToString('F1'))%"
    } elseif ($readinessScore -ge 60) {
        Add-TestResult -TestName "System Readiness" -Status "WARNING" -Details "Acceptable readiness: $($readinessScore.ToString('F1'))%"
    } else {
        Add-TestResult -TestName "System Readiness" -Status "FAILED" -Details "Poor readiness: $($readinessScore.ToString('F1'))%"
    }
    
} catch {
    Add-TestResult -TestName "System Readiness Assessment" -Status "FAILED" -Details "Could not assess readiness" -ErrorMessage $_.Exception.Message
}

# Test Summary
Write-Host "`n--- End-to-End Workflow Test Summary ---" -ForegroundColor Cyan

$passed = ($testResults | Where-Object { $_.Status -eq "PASSED" }).Count
$failed = ($testResults | Where-Object { $_.Status -eq "FAILED" }).Count
$warnings = ($testResults | Where-Object { $_.Status -eq "WARNING" }).Count

Write-Host "Total Tests: $($testResults.Count)" -ForegroundColor White
Write-Host "Passed: $passed" -ForegroundColor Green
Write-Host "Failed: $failed" -ForegroundColor Red
Write-Host "Warnings: $warnings" -ForegroundColor Yellow

$successRate = if ($testResults.Count -gt 0) {
    ($passed / $testResults.Count) * 100
} else { 0 }

Write-Host "Success Rate: $($successRate.ToString('F1'))%" -ForegroundColor $(if ($successRate -ge 80) { "Green" } elseif ($successRate -ge 60) { "Yellow" } else { "Red" })

# Final Assessment
Write-Host "`n--- Final System Assessment ---" -ForegroundColor Cyan

if ($successRate -ge 85) {
    Write-Host "🎉 SYSTEM READY FOR PRODUCTION DEPLOYMENT" -ForegroundColor Green
    Write-Host "All critical workflows are functional and the system is ready for live use." -ForegroundColor Green
} elseif ($successRate -ge 70) {
    Write-Host "⚠️ SYSTEM READY WITH MINOR ISSUES" -ForegroundColor Yellow
    Write-Host "Core functionality is available but some issues should be addressed." -ForegroundColor Yellow
} else {
    Write-Host "❌ SYSTEM NEEDS ATTENTION BEFORE DEPLOYMENT" -ForegroundColor Red
    Write-Host "Critical issues detected that must be resolved before production use." -ForegroundColor Red
}

Write-Host "`n--- End-to-End Workflow Test Complete ---" -ForegroundColor Cyan
