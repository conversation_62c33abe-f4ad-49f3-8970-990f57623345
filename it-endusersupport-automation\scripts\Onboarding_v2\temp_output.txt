[DEBUG] Starting JML script execution...
[DEBUG] Script directory: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\Onboarding_v2
Loading JML modules...
Modules Directory: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\Onboarding_v2\Modules
  Loading: JML-Configuration.psm1 [CRITICAL]
    Description: Core configuration management
    Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\Onboarding_v2\Modules\JML-Configuration.psm1
    Attempting to import module...
    [SUCCESS] Module loaded successfully
    Exported functions: 7
    Functions: ConvertTo-ConfigString, Get-ModuleConfiguration, Initialize-SmartConfiguration, Merge-ConfigurationWithDefaults, New-DefaultConfiguration, Save-ConfigurationFile, Test-InteractiveSession
  Loading: JML-Security.psm1 [CRITICAL]
    Description: Security and credential management
    Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\Onboarding_v2\Modules\JML-Security.psm1
    Attempting to import module...
    [SUCCESS] Module loaded successfully
    Exported functions: 3
    Functions: Get-SecureCredential, Get-StringHash, Protect-SensitiveData
  Loading: JML-Logging.psm1 [CRITICAL]
    Description: Secure logging functionality
    Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\Onboarding_v2\Modules\JML-Logging.psm1
    Attempting to import module...
    [SUCCESS] Module loaded successfully
    Exported functions: 4
    Functions: Get-CurrentLogPath, Initialize-SecureLogging, Write-Log, Write-SecureLog
  Loading: JML-Utilities.psm1 [CRITICAL]
    Description: Core utility functions
    Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\Onboarding_v2\Modules\JML-Utilities.psm1
    Attempting to import module...
    [SUCCESS] Module loaded successfully
    Exported functions: 6
    Functions: Confirm-InputSecurity, Confirm-RequiredModule, Get-CurrentUserName, Get-ValidatedUPN, New-SecurePassword, New-StandardUPN
  Loading: JML-ActiveDirectory.psm1 [OPTIONAL]
    Description: Active Directory operations
    Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\Onboarding_v2\Modules\JML-ActiveDirectory.psm1
    Attempting to import module...
    [ERROR] Failed to import module: The script 'JML-ActiveDirectory.psm1' cannot be run because the following modules that are specified by the "#requires" statements of the script are missing: ActiveDirectory.
    Exception Type: ScriptRequiresException
    [WARNING] Optional module failed to load - some features may be unavailable
  Loading: JML-Email.psm1 [OPTIONAL]
    Description: Email notification functionality
    Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\Onboarding_v2\Modules\JML-Email.psm1
    Attempting to import module...
    [SUCCESS] Module loaded successfully
    Exported functions: 4
    Functions: Send-DeletionEmailNotification, Send-EmailNotification, Send-EmailWithRetry, Send-ResetEmailNotification
  Loading: JML-Jira.psm1 [OPTIONAL]
    Description: Jira integration
    Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\Onboarding_v2\Modules\JML-Jira.psm1
    Attempting to import module...
    [SUCCESS] Module loaded successfully
    Exported functions: 11
    Functions: Add-EnhancedJiraAttachment, Add-EnhancedJiraComment, Format-JiraCommentADF, Format-JiraCommentWiki, Get-JiraCustomFieldValue, Get-JiraErrorCategory, Initialize-JiraConnection, Invoke-JiraOperationWithRetry, Test-JiraAttachmentValidation, Test-JiraErrorRetryable, Test-JiraTicketValidation

Module Loading Summary:
  Successfully loaded: 6/7
  Failed to load: 1
    Optional failures: 7

Failed Modules Details:
  - JML-ActiveDirectory.psm1 [OPTIONAL]
    Description: Active Directory operations
    Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\Onboarding_v2\Modules\JML-ActiveDirectory.psm1
    Error: Failed to import module: The script 'JML-ActiveDirectory.psm1' cannot be run because the following modules that are specified by the "#requires" statements of the script are missing: ActiveDirectory.
    Type: ScriptRequiresException

Core modules loaded successfully! Optional module failures will not prevent script execution.
Some features may be unavailable due to optional module failures.

WARNING: Some optional modules failed to load.
The script will continue, but some features may be unavailable:
  - JML-ActiveDirectory.psm1: Active Directory operations
    Impact: Active Directory operations will not be available

Direct call failed, re-importing module and trying again...
Configuration loaded from: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\Onboarding_v2\AdminAccountConfig.psd1
[INFO] JML System started in interactive mode

================================================================================
                    JML Admin Account Management System                      
                              Version 1.12                                    
================================================================================

Current User: JERAGM\akinje
Computer: JGM-CG7THR3
Configuration: Loaded
Jira Integration: Enabled

Available Operations:

  1. Create Admin Account
     Create a new admin account for a standard user

  2. Delete Admin Account
     Remove an existing admin account

  3. Reset Admin Account Password
     Reset the password for an existing admin account

  4. System Information
     Display version and module information

  5. Run Setup
     Configure credentials and validate environment

  6. Exit
     Exit the application

Enter your choice (1-6): 